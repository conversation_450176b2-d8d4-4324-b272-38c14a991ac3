"G:\JetBrains\JetBrains Rider 2024.3.5\plugins\dpa\DotFiles\JetBrains.DPA.Runner.exe" --handle=5660 --backend-pid=21088 --etw-collect-flags=67108622 --detach-event-name=dpa.detach.21088.36 --refresh-interval=1 -- G:/ZZZ/src/Main/Server/liteframe/bin/BattleServer.exe
MainThread Start.
MainThread LoadAndInitAppAssembly Start...
MainThread LoadAndInitAppAssembly End...
ThreadManager init begin.
ThreadManager init end.
[WorkUnit] WorkUnit 1 Start With Owner GlobalManager_ThreadManager.
=== 环境变量 ===
DOTNET_gcServer=
DOTNET_GCHeapCount: 
DOTNET_gcConcurrent: 
DOTNET_GCDynamicAdaptationMode: 
===================
=== .NET GC 配置 ===
GC 模式: Workstation GC
GC的LatencyMode模式: Interactive
LOH 压缩模式: Default
===================
BattleConfig initialized with PlayMode ID: 1, Type: 1, Scene: 1001
BattleConfig initialized successfully
NATS client configuration not found, using server connection for client
Status check timer started (10 second interval)
-------------【服务器程序加载完毕】-----------------------
------------------------------------------------------
[WorkUnit] WorkUnit 1 Enter Thread 0.
SceneManager Start
[WorkUnit] WorkUnit 2 Start With Owner GlobalManager_SceneManager.
[WorkUnit] WorkUnit 2 Enter Thread 31.
[BattleService] Player 10102021301 (奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player **********7 (AI_奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000058230 (AI_奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000069473 (AI_奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleStateManager_430554476511233] Initialized
[PlayerManager_430554476511233] Player 10102021301 (奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430554476511233] Player **********7 (AI_奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430554476511233] Player 90000058230 (AI_奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430554476511233] Player 90000069473 (AI_奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430554476511233] Initialized with 4 players
[BattleInstanceManager] Initialized for battle 430554476511233
[OpponentPairManager] Initialized for battle 430554476511233
[BuffManager_430554476511233] Initialized
[CheckerBoard_430554476511233] Cleared checkerboard
[CheckerBoard_430554476511233] Initialized
[AutoChessScene_430554476511233] Event handlers registered
[AutoChessScene_430554476511233] Battle 430554476511233 initialized with 4 players, waiting for all players to enter
[SceneManager] Added AutoChessScene 430554476511233 to thread management
[BattleService] Battle 430554476511233 created successfully with 4 players
[BattleService] Battle 430554476511233 is now waiting for all players to call EnterBattle RPC
[BattleService] Current battle state: StateNone (waiting for player entry)
[BattleService] Players expected to enter: [10102021301, **********7, 90000058230, 90000069473]
[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====
[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====
[BattleService] ===== BOT STRATEGY: Player 10102021301 is real player, others are bots =====
[BattleService] Auto-entered bot player **********7 immediately
[BattleService] Auto-entered bot player 90000058230 immediately
[BattleService] Auto-entered bot player 90000069473 immediately
[BattleService] Battle 430554476511233 bots auto-entered: 3/4 players ready
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021301 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021301 entered battle 430554476511233 (initial), current count: 4
[BattleService] All 4 players entered battle 430554476511233, starting battle state machine
[BattleService] Entered players: [**********7, 90000058230, 90000069473, 10102021301]
[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle 430554476511233
[AutoChessScene_430554476511233] StartBattleStateMachine() called for battle 430554476511233
[AutoChessScene_430554476511233] BattleStateManager is ready, starting first round...
[AutoChessScene_430554476511233] Current state before starting: StateNone
[BattleStateManager_430554476511233] ===== STARTING NEW ROUND 1 =====
[BattleStateManager_430554476511233] Round 1 has buff selection: False
[BattleStateManager_430554476511233] Publishing RoundStartedEvent for round 1
[AutoChessScene_430554476511233] Round 1 started
[BattleStateManager_430554476511233] Setting state to StateRoundStart for round 1
[BattleStateManager_430554476511233] State: StateNone -> StateRoundStart (R1, 1000ms)
[AutoChessScene_430554476511233] HandleRoundStart: 4 active players
[AutoChessScene_430554476511233] Valid player: 10102021301, Health: 3
[AutoChessScene_430554476511233] Valid player: **********7, Health: 3
[AutoChessScene_430554476511233] Valid player: 90000058230, Health: 3
[AutoChessScene_430554476511233] Valid player: 90000069473, Health: 3
[AutoChessScene_430554476511233] No instance found for player 10102021301 when saving board data
[AutoChessScene_430554476511233] No instance found for player **********7 when saving board data
[AutoChessScene_430554476511233] No instance found for player 90000058230 when saving board data
[AutoChessScene_430554476511233] No instance found for player 90000069473 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player **********7 vs Player 90000069473
[OpponentPairManager] Random pair: Player 90000058230 vs Player 10102021301
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_430554476511233] Created 4 opponent pairs
[PlayerManager_430554476511233] Set player opponents, count: 4
[BattleInstanceManager] Created instance 430554476511233_1 for active players **********7 vs 90000069473
[CheckerBoard_430554476511233] Cleared checkerboard
[CheckerBoard_430554476511233] Initialized
[BattleInstance] 430554476511233_1 created with players: **********7, 90000069473
[BattleInstanceManager] Created instance 430554476511233_2 for active players 90000058230 vs 10102021301
[CheckerBoard_430554476511233] Cleared checkerboard
[CheckerBoard_430554476511233] Initialized
[BattleInstance] 430554476511233_2 created with players: 90000058230, 10102021301
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_430554476511233] Cleaned orphaned entities for player 10102021301
[AutoChessScene_430554476511233] Cleaned orphaned entities for player **********7
[AutoChessScene_430554476511233] Cleaned orphaned entities for player 90000058230
[AutoChessScene_430554476511233] Cleaned orphaned entities for player 90000069473
[PlayerManager_430554476511233] Reset all players ready status
[AutoChessScene_430554476511233] Round started with 2 battle instances
[AutoChessScene_430554476511233] Generating 5 heroes for all players in round 1
[CheckerBoard_430554476511233] Placed entity 1 at position (8, 1)
[CheckerBoard_430554476511233] Created entity ID:1 ConfigID:102 at (8, 1) for player 10102021301
[CheckerBoard_430554476511233] Placed entity 2 at position (6, 6)
[CheckerBoard_430554476511233] Created entity ID:2 ConfigID:101 at (6, 6) for player 10102021301
[CheckerBoard_430554476511233] Placed entity 3 at position (7, 2)
[CheckerBoard_430554476511233] Created entity ID:3 ConfigID:101 at (7, 2) for player 10102021301
[CheckerBoard_430554476511233] Placed entity 4 at position (10, 1)
[CheckerBoard_430554476511233] Created entity ID:4 ConfigID:102 at (10, 1) for player 10102021301
[CheckerBoard_430554476511233] Placed entity 5 at position (10, 4)
[CheckerBoard_430554476511233] Created entity ID:5 ConfigID:101 at (10, 4) for player 10102021301
[CheckerBoard_430554476511233] Generated 5/5 heroes for player 10102021301: 5 placed, 0 in temporary slots
[CheckerBoard_430554476511233] Generated 5 heroes for player 10102021301 in Enemy area
[AutoChessScene_430554476511233] Generated 5 heroes for player 10102021301: 5 placed on board, 0 in temporary slots
[CheckerBoard_430554476511233] Placed entity 1 at position (3, 1)
[CheckerBoard_430554476511233] Created entity ID:1 ConfigID:102 at (3, 1) for player **********7
[CheckerBoard_430554476511233] Placed entity 2 at position (5, 1)
[CheckerBoard_430554476511233] Created entity ID:2 ConfigID:101 at (5, 1) for player **********7
[CheckerBoard_430554476511233] Placed entity 3 at position (1, 3)
[CheckerBoard_430554476511233] Created entity ID:3 ConfigID:101 at (1, 3) for player **********7
[CheckerBoard_430554476511233] Placed entity 4 at position (1, 2)
[CheckerBoard_430554476511233] Created entity ID:4 ConfigID:101 at (1, 2) for player **********7
[CheckerBoard_430554476511233] Placed entity 5 at position (2, 5)
[CheckerBoard_430554476511233] Created entity ID:5 ConfigID:101 at (2, 5) for player **********7
[CheckerBoard_430554476511233] CheckTimes limit (4) reached for 1 hero types for player **********7
[CheckerBoard_430554476511233] Generated 5/5 heroes for player **********7: 5 placed, 0 in temporary slots
[CheckerBoard_430554476511233] Generated 5 heroes for player **********7 in My area
[AutoChessScene_430554476511233] Generated 5 heroes for player **********7: 5 placed on board, 0 in temporary slots
[CheckerBoard_430554476511233] Placed entity 6 at position (5, 1)
[CheckerBoard_430554476511233] Created entity ID:6 ConfigID:103 at (5, 1) for player 90000058230
[CheckerBoard_430554476511233] Placed entity 7 at position (4, 2)
[CheckerBoard_430554476511233] Created entity ID:7 ConfigID:101 at (4, 2) for player 90000058230
[CheckerBoard_430554476511233] Placed entity 8 at position (3, 5)
[CheckerBoard_430554476511233] Created entity ID:8 ConfigID:101 at (3, 5) for player 90000058230
[CheckerBoard_430554476511233] Placed entity 9 at position (4, 6)
[CheckerBoard_430554476511233] Created entity ID:9 ConfigID:102 at (4, 6) for player 90000058230
[CheckerBoard_430554476511233] Placed entity 10 at position (4, 5)
[CheckerBoard_430554476511233] Created entity ID:10 ConfigID:101 at (4, 5) for player 90000058230
[CheckerBoard_430554476511233] Generated 5/5 heroes for player 90000058230: 5 placed, 0 in temporary slots
[CheckerBoard_430554476511233] Generated 5 heroes for player 90000058230 in My area
[AutoChessScene_430554476511233] Generated 5 heroes for player 90000058230: 5 placed on board, 0 in temporary slots
[CheckerBoard_430554476511233] Placed entity 6 at position (10, 6)
[CheckerBoard_430554476511233] Created entity ID:6 ConfigID:101 at (10, 6) for player 90000069473
[CheckerBoard_430554476511233] Placed entity 7 at position (9, 1)
[CheckerBoard_430554476511233] Created entity ID:7 ConfigID:101 at (9, 1) for player 90000069473
[CheckerBoard_430554476511233] Placed entity 8 at position (7, 5)
[CheckerBoard_430554476511233] Created entity ID:8 ConfigID:103 at (7, 5) for player 90000069473
[CheckerBoard_430554476511233] Placed entity 9 at position (7, 6)
[CheckerBoard_430554476511233] Created entity ID:9 ConfigID:101 at (7, 6) for player 90000069473
[CheckerBoard_430554476511233] Placed entity 10 at position (7, 4)
[CheckerBoard_430554476511233] Created entity ID:10 ConfigID:103 at (7, 4) for player 90000069473
[CheckerBoard_430554476511233] Generated 5/5 heroes for player 90000069473: 5 placed, 0 in temporary slots
[CheckerBoard_430554476511233] Generated 5 heroes for player 90000069473 in Enemy area
[AutoChessScene_430554476511233] Generated 5 heroes for player 90000069473: 5 placed on board, 0 in temporary slots
[AutoChessScene_430554476511233] Player status: Total=4, Active=4
[AutoChessScene_430554476511233] Player 10102021301: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430554476511233] Player **********7: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430554476511233] Player 90000058230: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430554476511233] Player 90000069473: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430554476511233] Sending RoundStart notifications to 4 active players...
[AutoChessScene_430554476511233] RoundStart board data: player:90000058230 heroes:5
[AutoChessScene_430554476511233] RoundStart board data: player:10102021301 heroes:5
[AutoChessScene_430554476511233] Sending RoundStart to Player 10102021301 on GameServer 10102
[AutoChessScene_430554476511233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430554476511233] RoundStart board data: player:**********7 heroes:5
[AutoChessScene_430554476511233] RoundStart board data: player:90000069473 heroes:5
[AutoChessScene_430554476511233] Sending RoundStart to Player **********7 on GameServer 10102
[AutoChessScene_430554476511233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430554476511233] RoundStart board data: player:90000058230 heroes:5
[AutoChessScene_430554476511233] RoundStart board data: player:10102021301 heroes:5
[AutoChessScene_430554476511233] Sending RoundStart to Player 90000058230 on GameServer 10102
[AutoChessScene_430554476511233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430554476511233] RoundStart board data: player:**********7 heroes:5
[AutoChessScene_430554476511233] RoundStart board data: player:90000069473 heroes:5
[AutoChessScene_430554476511233] Sending RoundStart to Player 90000069473 on GameServer 10102
[AutoChessScene_430554476511233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430554476511233] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 430554476511233 state to StateRoundStart
[AutoChessScene_430554476511233] State change sent to GameServer: StateNone -> StateRoundStart (R1)
[BattleStateManager_430554476511233] BattleStateChangedEvent published successfully
[BattleStateManager_430554476511233] ===== ROUND 1 INITIALIZATION COMPLETE =====
[AutoChessScene_430554476511233] Battle state machine started successfully for battle 430554476511233
[AutoChessScene_430554476511233] Current state after starting: StateRoundStart
[BattleService] Battle state update result: False, new state: StateRoundStart
[BattleStateManager_430554476511233] State: StateRoundStart -> StatePreparation (R1, 65000ms)
[AutoChessScene_430554476511233] Preparation phase started
[PlayerManager_430554476511233] Player **********7 ready status set to True
[PlayerManager_430554476511233] Player 90000058230 ready status set to True
[PlayerManager_430554476511233] Player 90000069473 ready status set to True
[AutoChessScene_430554476511233] Auto-ready 3 additional bots
[AutoChessScene_430554476511233] Free operation phase started
[BattleService] Updated battle 430554476511233 state to StatePreparation
[AutoChessScene_430554476511233] State change sent to GameServer: StateRoundStart -> StatePreparation (R1)
[BattleStateManager_430554476511233] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[PlayerManager_430554476511233] Player 10102021301 ready status set to True
[PlayerManager_430554476511233] All players are ready!
[AutoChessScene_430554476511233] All players are ready, transitioning to next state
[BattleStateManager_430554476511233] State: StatePreparation -> StateBattleStarting (R1, 1000ms)
[AutoChessScene_430554476511233] Applying battle start buffs for all players
[AutoChessScene_430554476511233] Camp info for player 90000058230: 5 heroes added
[AutoChessScene_430554476511233] Camp info for player 10102021301: 5 heroes added
[AutoChessScene_430554476511233] Created RoundBattleStart request for player 10102021301, Team order: [90000058230, 10102021301], total GridIDs used: 10
[AutoChessScene_430554476511233] Sent RoundBattleStart to Player 10102021301 vs Opponent 90000058230 with 2 teams
[AutoChessScene_430554476511233] Camp info for player **********7: 5 heroes added
[AutoChessScene_430554476511233] Camp info for player 90000069473: 5 heroes added
[AutoChessScene_430554476511233] Created RoundBattleStart request for player **********7, Team order: [**********7, 90000069473], total GridIDs used: 10
[AutoChessScene_430554476511233] Sent RoundBattleStart to Player **********7 vs Opponent 90000069473 with 2 teams
[AutoChessScene_430554476511233] Camp info for player 90000058230: 5 heroes added
[AutoChessScene_430554476511233] Camp info for player 10102021301: 5 heroes added
[AutoChessScene_430554476511233] Created RoundBattleStart request for player 90000058230, Team order: [90000058230, 10102021301], total GridIDs used: 10
[AutoChessScene_430554476511233] Sent RoundBattleStart to Player 90000058230 vs Opponent 10102021301 with 2 teams
[AutoChessScene_430554476511233] Camp info for player **********7: 5 heroes added
[AutoChessScene_430554476511233] Camp info for player 90000069473: 5 heroes added
[AutoChessScene_430554476511233] Created RoundBattleStart request for player 90000069473, Team order: [**********7, 90000069473], total GridIDs used: 10
[AutoChessScene_430554476511233] Sent RoundBattleStart to Player 90000069473 vs Opponent **********7 with 2 teams
[AutoChessScene_430554476511233] Sent RoundBattleStart notifications with seed: 1065252080
[BattleService] Updated battle 430554476511233 state to StateBattleStarting
[AutoChessScene_430554476511233] State change sent to GameServer: StatePreparation -> StateBattleStarting (R1)
[BattleStateManager_430554476511233] BattleStateChangedEvent published successfully
[AutoChessScene_430554476511233] Player 10102021301 set ready status to True
[BattleStateManager_430554476511233] State: StateBattleStarting -> StateBattleInProgress (R1, 65000ms)
[AutoChessScene_430554476511233] Starting all battle instances
[BattleInstance] 430554476511233_1 battle started
[BattleInstance] 430554476511233_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_430554476511233] Auto EndBattle for bot **********7 vs bot 90000069473, random result: bot **********7 wins = False
[AutoChessScene_430554476511233] Player **********7 sent EndBattleReq (win: False), instance: 430554476511233_1
[AutoChessScene_430554476511233] Waiting for opponent 90000069473 to send EndBattleReq for instance 430554476511233_1
[AutoChessScene_430554476511233] Player 90000069473 sent EndBattleReq (win: True), instance: 430554476511233_1
[BattleInstance] 430554476511233_1 battle finished, winner: 90000069473, loser: **********7
[AutoChessScene_430554476511233] Battle instance 430554476511233_1 completed: Winner 90000069473, Loser **********7
[AutoChessScene_430554476511233] Bot 90000058230 vs real player 10102021301, waiting for real player result
[AutoChessScene_430554476511233] Bot vs real player battles will be handled by system timeout (62s)
[BattleService] Updated battle 430554476511233 state to StateBattleInProgress
[AutoChessScene_430554476511233] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R1)
[BattleStateManager_430554476511233] BattleStateChangedEvent published successfully
[BattleService] EndBattle uid: 10102021301, win: True
[AutoChessScene_430554476511233] Player 10102021301 sent EndBattleReq (win: True), instance: 430554476511233_2
[AutoChessScene_430554476511233] Auto EndBattle for bot 90000058230 vs real player 10102021301, bot result: False
[BattleInstance] 430554476511233_2 battle finished, winner: 10102021301, loser: 90000058230
[AutoChessScene_430554476511233] Battle instance 430554476511233_2 completed: Winner 10102021301, Loser 90000058230
[AutoChessScene_430554476511233] All battle instances finished, proceeding to settlement
[BattleStateManager_430554476511233] State: StateBattleInProgress -> StateRoundSettlement (R1, 3000ms)
[AutoChessScene_430554476511233] Processing battle results
[PlayerManager_430554476511233] Player **********7 health reduced by 1, current health: 2
[AutoChessScene_430554476511233] Player **********7 lost 1 health, winner: 90000069473
[AutoChessScene_430554476511233] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000069473, Loser: **********7
[PlayerManager_430554476511233] Player 90000058230 health reduced by 1, current health: 2
[AutoChessScene_430554476511233] Player 90000058230 lost 1 health, winner: 10102021301
[AutoChessScene_430554476511233] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10102021301, Loser: 90000058230
[BattleStateManager_430554476511233] Invalid state transition from StateRoundSettlement to StateRoundSettlement
[AutoChessScene_430554476511233] Checking players elimination
[AutoChessScene_430554476511233] Active players remaining: 4
[AutoChessScene_430554476511233] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_430554476511233] Auto-confirming round settlement for bot **********7
[AutoChessScene_430554476511233] Auto-confirming round settlement for bot 90000058230
[AutoChessScene_430554476511233] Auto-confirming round settlement for bot 90000069473
[BattleService] Updated battle 430554476511233 state to StateRoundSettlement
[AutoChessScene_430554476511233] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R1)
[BattleStateManager_430554476511233] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_430554476511233] Battle timeout occurred in state StateRoundSettlement
[AutoChessScene_430554476511233] Round settlement timeout - force confirm all players
[AutoChessScene_430554476511233] Force confirming round settlement for player 10102021301 due to timeout
[AutoChessScene_430554476511233] Player 10102021301 confirmed round settlement, count: 4
[AutoChessScene_430554476511233] Real player 10102021301 confirmed, auto-confirming all bots
[AutoChessScene_430554476511233] All players confirmed round settlement, starting new round
[PlayerManager_430554476511233] Player 10102021301 ready status set to False
[PlayerManager_430554476511233] Player **********7 ready status set to False
[PlayerManager_430554476511233] Player 90000058230 ready status set to False
[PlayerManager_430554476511233] Player 90000069473 ready status set to False
[BattleStateManager_430554476511233] ===== STARTING NEW ROUND 2 =====
[BattleStateManager_430554476511233] Round 2 has buff selection: True
[BattleStateManager_430554476511233] Publishing RoundStartedEvent for round 2
[AutoChessScene_430554476511233] Round 2 started
[BattleStateManager_430554476511233] Setting state to StateRoundStart for round 2
[BattleStateManager_430554476511233] State: StateRoundSettlement -> StateRoundStart (R2, 1000ms)
[AutoChessScene_430554476511233] HandleRoundStart: 4 active players
[AutoChessScene_430554476511233] Valid player: 10102021301, Health: 3
[AutoChessScene_430554476511233] Valid player: **********7, Health: 2
[AutoChessScene_430554476511233] Valid player: 90000058230, Health: 2
[AutoChessScene_430554476511233] Valid player: 90000069473, Health: 3
[AutoChessScene_430554476511233] Player 10102021301 has 5 entities to save
[PlayerManager_430554476511233] Saved board data: player:10102021301 entities:5
[PlayerManager_430554476511233] Saved prev round data: player:10102021301 entities:5
[AutoChessScene_430554476511233] Player **********7 has 5 entities to save
[PlayerManager_430554476511233] Saved board data: player:**********7 entities:5
[PlayerManager_430554476511233] Saved prev round data: player:**********7 entities:5
[AutoChessScene_430554476511233] Player 90000058230 has 5 entities to save
[PlayerManager_430554476511233] Saved board data: player:90000058230 entities:5
[PlayerManager_430554476511233] Saved prev round data: player:90000058230 entities:5
[AutoChessScene_430554476511233] Player 90000069473 has 5 entities to save
[PlayerManager_430554476511233] Saved board data: player:90000069473 entities:5
[PlayerManager_430554476511233] Saved prev round data: player:90000069473 entities:5
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000069473 vs Player 90000058230
[OpponentPairManager] Random pair: Player **********7 vs Player 10102021301
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_430554476511233] Created 4 opponent pairs
[PlayerManager_430554476511233] Set player opponents, count: 4
[BattleInstanceManager] Created instance 430554476511233_1 for active players 90000069473 vs 90000058230
[CheckerBoard_430554476511233] Cleared checkerboard
[CheckerBoard_430554476511233] Initialized
[BattleInstance] 430554476511233_1 created with players: 90000069473, 90000058230
[BattleInstanceManager] Created instance 430554476511233_2 for active players **********7 vs 10102021301
[CheckerBoard_430554476511233] Cleared checkerboard
[CheckerBoard_430554476511233] Initialized
[BattleInstance] 430554476511233_2 created with players: **********7, 10102021301
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_430554476511233] Restoring player 10102021301 to Enemy area (rows 6-10)
[CheckerBoard_430554476511233] Placed entity 1 at position (8, 1)
[CheckerBoard_430554476511233] Created entity ID:1 ConfigID:102 at (8, 1) for player 10102021301
[AutoChessScene_430554476511233] Restored entity 1: (8,1)->(8,1), GridID:43->43
[CheckerBoard_430554476511233] Placed entity 2 at position (6, 6)
[CheckerBoard_430554476511233] Created entity ID:2 ConfigID:101 at (6, 6) for player 10102021301
[AutoChessScene_430554476511233] Restored entity 2: (6,6)->(6,6), GridID:36->36
[CheckerBoard_430554476511233] Placed entity 3 at position (7, 2)
[CheckerBoard_430554476511233] Created entity ID:3 ConfigID:101 at (7, 2) for player 10102021301
[AutoChessScene_430554476511233] Restored entity 3: (7,2)->(7,2), GridID:38->38
[CheckerBoard_430554476511233] Placed entity 4 at position (10, 1)
[CheckerBoard_430554476511233] Created entity ID:4 ConfigID:102 at (10, 1) for player 10102021301
[AutoChessScene_430554476511233] Restored entity 4: (10,1)->(10,1), GridID:55->55
[CheckerBoard_430554476511233] Placed entity 5 at position (10, 4)
[CheckerBoard_430554476511233] Created entity ID:5 ConfigID:101 at (10, 4) for player 10102021301
[AutoChessScene_430554476511233] Restored entity 5: (10,4)->(10,4), GridID:58->58
[AutoChessScene_430554476511233] Restored board: player:10102021301 entities:5
[AutoChessScene_430554476511233] Restoring player **********7 to My area (rows 1-5)
[CheckerBoard_430554476511233] Placed entity 6 at position (3, 1)
[CheckerBoard_430554476511233] Created entity ID:6 ConfigID:102 at (3, 1) for player **********7
[AutoChessScene_430554476511233] Restored entity 6: (3,1)->(3,1), GridID:13->13
[CheckerBoard_430554476511233] Placed entity 7 at position (5, 1)
[CheckerBoard_430554476511233] Created entity ID:7 ConfigID:101 at (5, 1) for player **********7
[AutoChessScene_430554476511233] Restored entity 7: (5,1)->(5,1), GridID:25->25
[CheckerBoard_430554476511233] Placed entity 8 at position (1, 3)
[CheckerBoard_430554476511233] Created entity ID:8 ConfigID:101 at (1, 3) for player **********7
[AutoChessScene_430554476511233] Restored entity 8: (1,3)->(1,3), GridID:3->3
[CheckerBoard_430554476511233] Placed entity 9 at position (1, 2)
[CheckerBoard_430554476511233] Created entity ID:9 ConfigID:101 at (1, 2) for player **********7
[AutoChessScene_430554476511233] Restored entity 9: (1,2)->(1,2), GridID:2->2
[CheckerBoard_430554476511233] Placed entity 10 at position (2, 5)
[CheckerBoard_430554476511233] Created entity ID:10 ConfigID:101 at (2, 5) for player **********7
[AutoChessScene_430554476511233] Restored entity 10: (2,5)->(2,5), GridID:11->11
[AutoChessScene_430554476511233] Restored board: player:**********7 entities:5
[AutoChessScene_430554476511233] Restoring player 90000058230 to Enemy area (rows 6-10)
[CheckerBoard_430554476511233] Placed entity 1 at position (10, 1)
[CheckerBoard_430554476511233] Created entity ID:1 ConfigID:103 at (10, 1) for player 90000058230
[AutoChessScene_430554476511233] Restored entity 1: (5,1)->(10,1), GridID:25->55
[CheckerBoard_430554476511233] Placed entity 2 at position (9, 2)
[CheckerBoard_430554476511233] Created entity ID:2 ConfigID:101 at (9, 2) for player 90000058230
[AutoChessScene_430554476511233] Restored entity 2: (4,2)->(9,2), GridID:20->50
[CheckerBoard_430554476511233] Placed entity 3 at position (8, 5)
[CheckerBoard_430554476511233] Created entity ID:3 ConfigID:101 at (8, 5) for player 90000058230
[AutoChessScene_430554476511233] Restored entity 3: (3,5)->(8,5), GridID:17->47
[CheckerBoard_430554476511233] Placed entity 4 at position (9, 6)
[CheckerBoard_430554476511233] Created entity ID:4 ConfigID:102 at (9, 6) for player 90000058230
[AutoChessScene_430554476511233] Restored entity 4: (4,6)->(9,6), GridID:24->54
[CheckerBoard_430554476511233] Placed entity 5 at position (9, 5)
[CheckerBoard_430554476511233] Created entity ID:5 ConfigID:101 at (9, 5) for player 90000058230
[AutoChessScene_430554476511233] Restored entity 5: (4,5)->(9,5), GridID:23->53
[AutoChessScene_430554476511233] Restored board: player:90000058230 entities:5
[AutoChessScene_430554476511233] Restoring player 90000069473 to My area (rows 1-5)
[CheckerBoard_430554476511233] Placed entity 6 at position (5, 6)
[CheckerBoard_430554476511233] Created entity ID:6 ConfigID:101 at (5, 6) for player 90000069473
[AutoChessScene_430554476511233] Restored entity 6: (10,6)->(5,6), GridID:60->30
[CheckerBoard_430554476511233] Placed entity 7 at position (4, 1)
[CheckerBoard_430554476511233] Created entity ID:7 ConfigID:101 at (4, 1) for player 90000069473
[AutoChessScene_430554476511233] Restored entity 7: (9,1)->(4,1), GridID:49->19
[CheckerBoard_430554476511233] Placed entity 8 at position (2, 5)
[CheckerBoard_430554476511233] Created entity ID:8 ConfigID:103 at (2, 5) for player 90000069473
[AutoChessScene_430554476511233] Restored entity 8: (7,5)->(2,5), GridID:41->11
[CheckerBoard_430554476511233] Placed entity 9 at position (2, 6)
[CheckerBoard_430554476511233] Created entity ID:9 ConfigID:101 at (2, 6) for player 90000069473
[AutoChessScene_430554476511233] Restored entity 9: (7,6)->(2,6), GridID:42->12
[CheckerBoard_430554476511233] Placed entity 10 at position (2, 4)
[CheckerBoard_430554476511233] Created entity ID:10 ConfigID:103 at (2, 4) for player 90000069473
[AutoChessScene_430554476511233] Restored entity 10: (7,4)->(2,4), GridID:40->10
[AutoChessScene_430554476511233] Restored board: player:90000069473 entities:5
[AutoChessScene_430554476511233] Cleaned orphaned entities for player 10102021301
[AutoChessScene_430554476511233] Cleaned orphaned entities for player **********7
[AutoChessScene_430554476511233] Cleaned orphaned entities for player 90000058230
[AutoChessScene_430554476511233] Cleaned orphaned entities for player 90000069473
[PlayerManager_430554476511233] Reset all players ready status
[AutoChessScene_430554476511233] Round started with 2 battle instances
[AutoChessScene_430554476511233] Generating buff options for all players
[BuffManager_430554476511233] Generated 3 buff options for player 10102021301: [107, 102, 104]
[AutoChessScene_430554476511233] Generated 3 buff options for player 10102021301: [107, 102, 104]
[BuffManager_430554476511233] Generated 3 buff options for player **********7: [106, 102, 101]
[AutoChessScene_430554476511233] Generated 3 buff options for player **********7: [106, 102, 101]
[BuffManager_430554476511233] Generated 3 buff options for player 90000058230: [108, 105, 101]
[AutoChessScene_430554476511233] Generated 3 buff options for player 90000058230: [108, 105, 101]
[BuffManager_430554476511233] Generated 3 buff options for player 90000069473: [109, 101, 107]
[AutoChessScene_430554476511233] Generated 3 buff options for player 90000069473: [109, 101, 107]
[AutoChessScene_430554476511233] Player status: Total=4, Active=4
[AutoChessScene_430554476511233] Player 10102021301: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430554476511233] Player **********7: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_430554476511233] Player 90000058230: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_430554476511233] Player 90000069473: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430554476511233] Sending RoundStart notifications to 4 active players...
[AutoChessScene_430554476511233] RoundStart: Player 10102021301 buff options: [107, 102, 104]
[AutoChessScene_430554476511233] RoundStart board data: player:**********7 heroes:5
[AutoChessScene_430554476511233] RoundStart board data: player:10102021301 heroes:5
[AutoChessScene_430554476511233] Sending RoundStart to Player 10102021301 on GameServer 10102
[AutoChessScene_430554476511233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430554476511233] RoundStart: Player **********7 buff options: [106, 102, 101]
[AutoChessScene_430554476511233] RoundStart board data: player:**********7 heroes:5
[AutoChessScene_430554476511233] RoundStart board data: player:10102021301 heroes:5
[AutoChessScene_430554476511233] Sending RoundStart to Player **********7 on GameServer 10102
[AutoChessScene_430554476511233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430554476511233] RoundStart: Player 90000058230 buff options: [108, 105, 101]
[AutoChessScene_430554476511233] RoundStart board data: player:90000069473 heroes:5
[AutoChessScene_430554476511233] RoundStart board data: player:90000058230 heroes:5
[AutoChessScene_430554476511233] Sending RoundStart to Player 90000058230 on GameServer 10102
[AutoChessScene_430554476511233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430554476511233] RoundStart: Player 90000069473 buff options: [109, 101, 107]
[AutoChessScene_430554476511233] RoundStart board data: player:90000069473 heroes:5
[AutoChessScene_430554476511233] RoundStart board data: player:90000058230 heroes:5
[AutoChessScene_430554476511233] Sending RoundStart to Player 90000069473 on GameServer 10102
[AutoChessScene_430554476511233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430554476511233] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 430554476511233 state to StateRoundStart
[AutoChessScene_430554476511233] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R2)
[BattleStateManager_430554476511233] BattleStateChangedEvent published successfully
[BattleStateManager_430554476511233] ===== ROUND 2 INITIALIZATION COMPLETE =====
[AutoChessScene_430554476511233] Force confirming round settlement for player **********7 due to timeout
[AutoChessScene_430554476511233] Cannot handle round confirmation in state StateRoundStart
[AutoChessScene_430554476511233] Force confirming round settlement for player 90000058230 due to timeout
[AutoChessScene_430554476511233] Cannot handle round confirmation in state StateRoundStart
[AutoChessScene_430554476511233] Force confirming round settlement for player 90000069473 due to timeout
[AutoChessScene_430554476511233] Cannot handle round confirmation in state StateRoundStart
[BattleStateManager_430554476511233] Published BattleTimeoutEvent for state StateRoundStart
[BattleStateManager_430554476511233] Buff selection timer started: 25000ms
[BattleStateManager_430554476511233] State: StateRoundStart -> StatePreparation (R2, 65000ms)
[AutoChessScene_430554476511233] Preparation phase started
[BuffManager_430554476511233] Added buff 106 (Buff_106) to player **********7
[AutoChessScene_430554476511233] Auto-selected buff 106 for bot player **********7
[CheckerBoard_430554476511233] Placed entity 11 at position (1, 5)
[CheckerBoard_430554476511233] Created entity ID:11 ConfigID:102 at (1, 5) for player **********7
[CheckerBoard_430554476511233] Placed entity 12 at position (5, 2)
[CheckerBoard_430554476511233] Created entity ID:12 ConfigID:102 at (5, 2) for player **********7
[CheckerBoard_430554476511233] Placed entity 13 at position (3, 6)
[CheckerBoard_430554476511233] Created entity ID:13 ConfigID:102 at (3, 6) for player **********7
[CheckerBoard_430554476511233] Placed entity 14 at position (1, 6)
[CheckerBoard_430554476511233] Created entity ID:14 ConfigID:103 at (1, 6) for player **********7
[CheckerBoard_430554476511233] Placed entity 15 at position (2, 6)
[CheckerBoard_430554476511233] Created entity ID:15 ConfigID:103 at (2, 6) for player **********7
[CheckerBoard_430554476511233] Generated 5/5 heroes for player **********7: 5 placed, 0 in temporary slots
[CheckerBoard_430554476511233] Generated 5 heroes for player **********7 in My area
[AutoChessScene_430554476511233] Generated 5 heroes for player **********7: 5 placed on board, 0 in temporary slots
[AutoChessScene_430554476511233] Generated 5 new heroes for bot player **********7 after buff selection
[BuffManager_430554476511233] Added buff 108 (Buff_108) to player 90000058230
[AutoChessScene_430554476511233] Auto-selected buff 108 for bot player 90000058230
[CheckerBoard_430554476511233] Placed entity 11 at position (6, 5)
[CheckerBoard_430554476511233] Created entity ID:11 ConfigID:102 at (6, 5) for player 90000058230
[CheckerBoard_430554476511233] Placed entity 12 at position (7, 6)
[CheckerBoard_430554476511233] Created entity ID:12 ConfigID:103 at (7, 6) for player 90000058230
[CheckerBoard_430554476511233] Placed entity 13 at position (7, 5)
[CheckerBoard_430554476511233] Created entity ID:13 ConfigID:101 at (7, 5) for player 90000058230
[CheckerBoard_430554476511233] Placed entity 14 at position (10, 4)
[CheckerBoard_430554476511233] Created entity ID:14 ConfigID:101 at (10, 4) for player 90000058230
[CheckerBoard_430554476511233] Placed entity 15 at position (10, 3)
[CheckerBoard_430554476511233] Created entity ID:15 ConfigID:102 at (10, 3) for player 90000058230
[CheckerBoard_430554476511233] Generated 5/5 heroes for player 90000058230: 5 placed, 0 in temporary slots
[CheckerBoard_430554476511233] Generated 5 heroes for player 90000058230 in Enemy area
[AutoChessScene_430554476511233] Generated 5 heroes for player 90000058230: 5 placed on board, 0 in temporary slots
[AutoChessScene_430554476511233] Generated 5 new heroes for bot player 90000058230 after buff selection
[BuffManager_430554476511233] Added buff 109 (Buff_109) to player 90000069473
[AutoChessScene_430554476511233] Auto-selected buff 109 for bot player 90000069473
[CheckerBoard_430554476511233] Placed entity 16 at position (5, 3)
[CheckerBoard_430554476511233] Created entity ID:16 ConfigID:102 at (5, 3) for player 90000069473
[CheckerBoard_430554476511233] Placed entity 17 at position (1, 2)
[CheckerBoard_430554476511233] Created entity ID:17 ConfigID:103 at (1, 2) for player 90000069473
[CheckerBoard_430554476511233] Placed entity 18 at position (3, 5)
[CheckerBoard_430554476511233] Created entity ID:18 ConfigID:102 at (3, 5) for player 90000069473
[CheckerBoard_430554476511233] Placed entity 19 at position (1, 5)
[CheckerBoard_430554476511233] Created entity ID:19 ConfigID:103 at (1, 5) for player 90000069473
[CheckerBoard_430554476511233] Placed entity 20 at position (4, 2)
[CheckerBoard_430554476511233] Created entity ID:20 ConfigID:102 at (4, 2) for player 90000069473
[CheckerBoard_430554476511233] Generated 5/5 heroes for player 90000069473: 5 placed, 0 in temporary slots
[CheckerBoard_430554476511233] Generated 5 heroes for player 90000069473 in My area
[AutoChessScene_430554476511233] Generated 5 heroes for player 90000069473: 5 placed on board, 0 in temporary slots
[AutoChessScene_430554476511233] Generated 5 new heroes for bot player 90000069473 after buff selection
[PlayerManager_430554476511233] Player **********7 ready status set to True
[PlayerManager_430554476511233] Player 90000058230 ready status set to True
[PlayerManager_430554476511233] Player 90000069473 ready status set to True
[AutoChessScene_430554476511233] Auto-ready 3 additional bots
[AutoChessScene_430554476511233] Free operation phase started
[BattleService] Updated battle 430554476511233 state to StatePreparation
[AutoChessScene_430554476511233] State change sent to GameServer: StateRoundStart -> StatePreparation (R2)
[BattleStateManager_430554476511233] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021301 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021301 confirmed round settlement, current state: StatePreparation
[AutoChessScene_430554476511233] Cannot handle round confirmation in state StatePreparation
[BuffManager_430554476511233] Added buff 102 (Buff_102) to player 10102021301
[CheckerBoard_430554476511233] Placed entity 16 at position (9, 5)
[CheckerBoard_430554476511233] Created entity ID:16 ConfigID:101 at (9, 5) for player 10102021301
[CheckerBoard_430554476511233] Placed entity 17 at position (7, 5)
[CheckerBoard_430554476511233] Created entity ID:17 ConfigID:102 at (7, 5) for player 10102021301
[CheckerBoard_430554476511233] Placed entity 18 at position (7, 4)
[CheckerBoard_430554476511233] Created entity ID:18 ConfigID:103 at (7, 4) for player 10102021301
[CheckerBoard_430554476511233] Placed entity 19 at position (6, 1)
[CheckerBoard_430554476511233] Created entity ID:19 ConfigID:103 at (6, 1) for player 10102021301
[CheckerBoard_430554476511233] Placed entity 20 at position (10, 2)
[CheckerBoard_430554476511233] Created entity ID:20 ConfigID:103 at (10, 2) for player 10102021301
[CheckerBoard_430554476511233] Generated 5/5 heroes for player 10102021301: 5 placed, 0 in temporary slots
[CheckerBoard_430554476511233] Generated 5 heroes for player 10102021301 in Enemy area
[AutoChessScene_430554476511233] Generated 5 heroes for player 10102021301: 5 placed on board, 0 in temporary slots
[AutoChessScene_430554476511233] Player 10102021301 selected buff 102, generated 5 new heroes
[BuffManager_430554476511233] No buff options available for player 10102021301
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BuffManager_430554476511233] No buff options available for player 10102021301
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[Memory] Entity Reference Using Total Recently Rise Above 128 !

Process finished with exit code -1,073,741,510.

