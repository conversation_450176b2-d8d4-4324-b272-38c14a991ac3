[BattleService] Player 10102021301 (奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000098597 (AI_奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000029424 (AI_奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000083579 (AI_奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleStateManager_430500604870657] Initialized
[PlayerManager_430500604870657] Player 10102021301 (奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430500604870657] Player 90000098597 (AI_奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430500604870657] Player 90000029424 (AI_奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430500604870657] Player 90000083579 (AI_奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430500604870657] Initialized with 4 players
[BattleInstanceManager] Initialized for battle 430500604870657
[OpponentPairManager] Initialized for battle 430500604870657
[BuffManager_430500604870657] Initialized
[CheckerBoard_430500604870657] Cleared checkerboard
[CheckerBoard_430500604870657] Initialized
[AutoChessScene_430500604870657] Event handlers registered
[AutoChessScene_430500604870657] Battle 430500604870657 initialized with 4 players, waiting for all players to enter
[SceneManager] Added AutoChessScene 430500604870657 to thread management
[BattleService] Battle 430500604870657 created successfully with 4 players
[BattleService] Battle 430500604870657 is now waiting for all players to call EnterBattle RPC
[BattleService] Current battle state: StateNone (waiting for player entry)
[BattleService] Players expected to enter: [10102021301, 90000098597, 90000029424, 90000083579]
[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====
[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====
[BattleService] ===== BOT STRATEGY: Player 10102021301 is real player, others are bots =====
[BattleService] Auto-entered bot player 90000098597 immediately
[BattleService] Auto-entered bot player 90000029424 immediately
[BattleService] Auto-entered bot player 90000083579 immediately
[BattleService] Battle 430500604870657 bots auto-entered: 3/4 players ready
[BattleService] Status: 1 waiting (3/4 entered), 0 active
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021301 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021301 entered battle 430500604870657 (initial), current count: 4
[BattleService] All 4 players entered battle 430500604870657, starting battle state machine
[BattleService] Entered players: [90000098597, 90000029424, 90000083579, 10102021301]
[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle 430500604870657
[AutoChessScene_430500604870657] StartBattleStateMachine() called for battle 430500604870657
[AutoChessScene_430500604870657] BattleStateManager is ready, starting first round...
[AutoChessScene_430500604870657] Current state before starting: StateNone
[BattleStateManager_430500604870657] ===== STARTING NEW ROUND 1 =====
[BattleStateManager_430500604870657] Round 1 has buff selection: False
[BattleStateManager_430500604870657] Publishing RoundStartedEvent for round 1
[AutoChessScene_430500604870657] Round 1 started
[BattleStateManager_430500604870657] Setting state to StateRoundStart for round 1
[BattleStateManager_430500604870657] State: StateNone -> StateRoundStart (R1, 1000ms)
[AutoChessScene_430500604870657] HandleRoundStart: 4 active players
[AutoChessScene_430500604870657] Valid player: 10102021301, Health: 3
[AutoChessScene_430500604870657] Valid player: 90000098597, Health: 3
[AutoChessScene_430500604870657] Valid player: 90000029424, Health: 3
[AutoChessScene_430500604870657] Valid player: 90000083579, Health: 3
[AutoChessScene_430500604870657] No instance found for player 10102021301 when saving board data
[AutoChessScene_430500604870657] No instance found for player 90000098597 when saving board data
[AutoChessScene_430500604870657] No instance found for player 90000029424 when saving board data
[AutoChessScene_430500604870657] No instance found for player 90000083579 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000083579 vs Player 90000098597
[OpponentPairManager] Random pair: Player 10102021301 vs Player 90000029424
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_430500604870657] Created 4 opponent pairs
[PlayerManager_430500604870657] Set player opponents, count: 4
[BattleInstanceManager] Created instance 430500604870657_1 for active players 90000083579 vs 90000098597
[CheckerBoard_430500604870657] Cleared checkerboard
[CheckerBoard_430500604870657] Initialized
[BattleInstance] 430500604870657_1 created with players: 90000083579, 90000098597
[BattleInstanceManager] Created instance 430500604870657_2 for active players 10102021301 vs 90000029424
[CheckerBoard_430500604870657] Cleared checkerboard
[CheckerBoard_430500604870657] Initialized
[BattleInstance] 430500604870657_2 created with players: 10102021301, 90000029424
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_430500604870657] Cleaned orphaned entities for player 10102021301
[AutoChessScene_430500604870657] Cleaned orphaned entities for player 90000098597
[AutoChessScene_430500604870657] Cleaned orphaned entities for player 90000029424
[AutoChessScene_430500604870657] Cleaned orphaned entities for player 90000083579
[PlayerManager_430500604870657] Reset all players ready status
[AutoChessScene_430500604870657] Round started with 2 battle instances
[AutoChessScene_430500604870657] Generating 5 heroes for all players in round 1
[CheckerBoard_430500604870657] Placed entity 1 at position (5, 3)
[CheckerBoard_430500604870657] Created entity ID:1 ConfigID:102 at (5, 3) for player 10102021301
[CheckerBoard_430500604870657] Placed entity 2 at position (2, 5)
[CheckerBoard_430500604870657] Created entity ID:2 ConfigID:101 at (2, 5) for player 10102021301
[CheckerBoard_430500604870657] Placed entity 3 at position (2, 3)
[CheckerBoard_430500604870657] Created entity ID:3 ConfigID:101 at (2, 3) for player 10102021301
[CheckerBoard_430500604870657] Placed entity 4 at position (3, 4)
[CheckerBoard_430500604870657] Created entity ID:4 ConfigID:101 at (3, 4) for player 10102021301
[CheckerBoard_430500604870657] Placed entity 5 at position (1, 1)
[CheckerBoard_430500604870657] Created entity ID:5 ConfigID:102 at (1, 1) for player 10102021301
[CheckerBoard_430500604870657] Generated 5/5 heroes for player 10102021301: 5 placed, 0 in temporary slots
[CheckerBoard_430500604870657] Generated 5 heroes for player 10102021301 in My area
[AutoChessScene_430500604870657] Generated 5 heroes for player 10102021301: 5 placed on board, 0 in temporary slots
[CheckerBoard_430500604870657] Placed entity 1 at position (9, 2)
[CheckerBoard_430500604870657] Created entity ID:1 ConfigID:103 at (9, 2) for player 90000098597
[CheckerBoard_430500604870657] Placed entity 2 at position (6, 1)
[CheckerBoard_430500604870657] Created entity ID:2 ConfigID:101 at (6, 1) for player 90000098597
[CheckerBoard_430500604870657] Placed entity 3 at position (6, 2)
[CheckerBoard_430500604870657] Created entity ID:3 ConfigID:101 at (6, 2) for player 90000098597
[CheckerBoard_430500604870657] Placed entity 4 at position (7, 4)
[CheckerBoard_430500604870657] Created entity ID:4 ConfigID:102 at (7, 4) for player 90000098597
[CheckerBoard_430500604870657] Placed entity 5 at position (8, 5)
[CheckerBoard_430500604870657] Created entity ID:5 ConfigID:102 at (8, 5) for player 90000098597
[CheckerBoard_430500604870657] Generated 5/5 heroes for player 90000098597: 5 placed, 0 in temporary slots
[CheckerBoard_430500604870657] Generated 5 heroes for player 90000098597 in Enemy area
[AutoChessScene_430500604870657] Generated 5 heroes for player 90000098597: 5 placed on board, 0 in temporary slots
[CheckerBoard_430500604870657] Placed entity 6 at position (10, 5)
[CheckerBoard_430500604870657] Created entity ID:6 ConfigID:102 at (10, 5) for player 90000029424
[CheckerBoard_430500604870657] Placed entity 7 at position (9, 4)
[CheckerBoard_430500604870657] Created entity ID:7 ConfigID:102 at (9, 4) for player 90000029424
[CheckerBoard_430500604870657] Placed entity 8 at position (10, 4)
[CheckerBoard_430500604870657] Created entity ID:8 ConfigID:102 at (10, 4) for player 90000029424
[CheckerBoard_430500604870657] Placed entity 9 at position (6, 2)
[CheckerBoard_430500604870657] Created entity ID:9 ConfigID:103 at (6, 2) for player 90000029424
[CheckerBoard_430500604870657] Placed entity 10 at position (6, 1)
[CheckerBoard_430500604870657] Created entity ID:10 ConfigID:101 at (6, 1) for player 90000029424
[CheckerBoard_430500604870657] Generated 5/5 heroes for player 90000029424: 5 placed, 0 in temporary slots
[CheckerBoard_430500604870657] Generated 5 heroes for player 90000029424 in Enemy area
[AutoChessScene_430500604870657] Generated 5 heroes for player 90000029424: 5 placed on board, 0 in temporary slots
[CheckerBoard_430500604870657] Placed entity 6 at position (1, 5)
[CheckerBoard_430500604870657] Created entity ID:6 ConfigID:101 at (1, 5) for player 90000083579
[CheckerBoard_430500604870657] Placed entity 7 at position (4, 5)
[CheckerBoard_430500604870657] Created entity ID:7 ConfigID:102 at (4, 5) for player 90000083579
[CheckerBoard_430500604870657] Placed entity 8 at position (2, 6)
[CheckerBoard_430500604870657] Created entity ID:8 ConfigID:102 at (2, 6) for player 90000083579
[CheckerBoard_430500604870657] Placed entity 9 at position (5, 2)
[CheckerBoard_430500604870657] Created entity ID:9 ConfigID:102 at (5, 2) for player 90000083579
[CheckerBoard_430500604870657] Placed entity 10 at position (4, 2)
[CheckerBoard_430500604870657] Created entity ID:10 ConfigID:103 at (4, 2) for player 90000083579
[CheckerBoard_430500604870657] Generated 5/5 heroes for player 90000083579: 5 placed, 0 in temporary slots
[CheckerBoard_430500604870657] Generated 5 heroes for player 90000083579 in My area
[AutoChessScene_430500604870657] Generated 5 heroes for player 90000083579: 5 placed on board, 0 in temporary slots
[AutoChessScene_430500604870657] Player status: Total=4, Active=4
[AutoChessScene_430500604870657] Player 10102021301: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430500604870657] Player 90000098597: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430500604870657] Player 90000029424: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430500604870657] Player 90000083579: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430500604870657] Sending RoundStart notifications to 4 active players...
[AutoChessScene_430500604870657] RoundStart board data: player:10102021301 heroes:5
[AutoChessScene_430500604870657] RoundStart board data: player:90000029424 heroes:5
[AutoChessScene_430500604870657] Sending RoundStart to Player 10102021301 on GameServer 10102
[AutoChessScene_430500604870657] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430500604870657] RoundStart board data: player:90000083579 heroes:5
[AutoChessScene_430500604870657] RoundStart board data: player:90000098597 heroes:5
[AutoChessScene_430500604870657] Sending RoundStart to Player 90000098597 on GameServer 10102
[AutoChessScene_430500604870657] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430500604870657] RoundStart board data: player:10102021301 heroes:5
[AutoChessScene_430500604870657] RoundStart board data: player:90000029424 heroes:5
[AutoChessScene_430500604870657] Sending RoundStart to Player 90000029424 on GameServer 10102
[AutoChessScene_430500604870657] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430500604870657] RoundStart board data: player:90000083579 heroes:5
[AutoChessScene_430500604870657] RoundStart board data: player:90000098597 heroes:5
[AutoChessScene_430500604870657] Sending RoundStart to Player 90000083579 on GameServer 10102
[AutoChessScene_430500604870657] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430500604870657] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 430500604870657 state to StateRoundStart
[AutoChessScene_430500604870657] State change sent to GameServer: StateNone -> StateRoundStart (R1)
[BattleStateManager_430500604870657] BattleStateChangedEvent published successfully
[BattleStateManager_430500604870657] ===== ROUND 1 INITIALIZATION COMPLETE =====
[AutoChessScene_430500604870657] Battle state machine started successfully for battle 430500604870657
[AutoChessScene_430500604870657] Current state after starting: StateRoundStart
[BattleService] Battle state update result: False, new state: StateRoundStart
[BattleStateManager_430500604870657] State: StateRoundStart -> StatePreparation (R1, 65000ms)
[AutoChessScene_430500604870657] Preparation phase started
[PlayerManager_430500604870657] Player 90000098597 ready status set to True
[PlayerManager_430500604870657] Player 90000029424 ready status set to True
[PlayerManager_430500604870657] Player 90000083579 ready status set to True
[AutoChessScene_430500604870657] Auto-ready 3 additional bots
[AutoChessScene_430500604870657] Free operation phase started
[BattleService] Updated battle 430500604870657 state to StatePreparation
[AutoChessScene_430500604870657] State change sent to GameServer: StateRoundStart -> StatePreparation (R1)
[BattleStateManager_430500604870657] BattleStateChangedEvent published successfully
[PlayerManager_430500604870657] Player 10102021301 ready status set to True
[PlayerManager_430500604870657] All players are ready!
[AutoChessScene_430500604870657] All players are ready, transitioning to next state
[BattleStateManager_430500604870657] State: StatePreparation -> StateBattleStarting (R1, 1000ms)
[AutoChessScene_430500604870657] Applying battle start buffs for all players
[AutoChessScene_430500604870657] Camp info for player 10102021301: 5 heroes added
[AutoChessScene_430500604870657] Camp info for player 90000029424: 5 heroes added
[AutoChessScene_430500604870657] Created RoundBattleStart request for player 10102021301, Team order: [10102021301, 90000029424], total GridIDs used: 10
[AutoChessScene_430500604870657] Sent RoundBattleStart to Player 10102021301 vs Opponent 90000029424 with 2 teams
[AutoChessScene_430500604870657] Camp info for player 90000083579: 5 heroes added
[AutoChessScene_430500604870657] Camp info for player 90000098597: 5 heroes added
[AutoChessScene_430500604870657] Created RoundBattleStart request for player 90000098597, Team order: [90000083579, 90000098597], total GridIDs used: 10
[AutoChessScene_430500604870657] Sent RoundBattleStart to Player 90000098597 vs Opponent 90000083579 with 2 teams
[AutoChessScene_430500604870657] Camp info for player 10102021301: 5 heroes added
[AutoChessScene_430500604870657] Camp info for player 90000029424: 5 heroes added
[AutoChessScene_430500604870657] Created RoundBattleStart request for player 90000029424, Team order: [10102021301, 90000029424], total GridIDs used: 10
[AutoChessScene_430500604870657] Sent RoundBattleStart to Player 90000029424 vs Opponent 10102021301 with 2 teams
[AutoChessScene_430500604870657] Camp info for player 90000083579: 5 heroes added
[AutoChessScene_430500604870657] Camp info for player 90000098597: 5 heroes added
[AutoChessScene_430500604870657] Created RoundBattleStart request for player 90000083579, Team order: [90000083579, 90000098597], total GridIDs used: 10
[AutoChessScene_430500604870657] Sent RoundBattleStart to Player 90000083579 vs Opponent 90000098597 with 2 teams
[AutoChessScene_430500604870657] Sent RoundBattleStart notifications with seed: 1330424653
[BattleService] Updated battle 430500604870657 state to StateBattleStarting
[AutoChessScene_430500604870657] State change sent to GameServer: StatePreparation -> StateBattleStarting (R1)
[BattleStateManager_430500604870657] BattleStateChangedEvent published successfully
[AutoChessScene_430500604870657] Player 10102021301 set ready status to True
[BattleStateManager_430500604870657] State: StateBattleStarting -> StateBattleInProgress (R1, 65000ms)
[AutoChessScene_430500604870657] Starting all battle instances
[BattleInstance] 430500604870657_1 battle started
[BattleInstance] 430500604870657_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_430500604870657] Auto EndBattle for bot 90000098597 vs bot 90000083579, random result: bot 90000098597 wins = True
[AutoChessScene_430500604870657] Player 90000098597 sent EndBattleReq (win: True), instance: 430500604870657_1
[AutoChessScene_430500604870657] Waiting for opponent 90000083579 to send EndBattleReq for instance 430500604870657_1
[AutoChessScene_430500604870657] Player 90000083579 sent EndBattleReq (win: False), instance: 430500604870657_1
[BattleInstance] 430500604870657_1 battle finished, winner: 90000098597, loser: 90000083579
[AutoChessScene_430500604870657] Battle instance 430500604870657_1 completed: Winner 90000098597, Loser 90000083579
[AutoChessScene_430500604870657] Bot 90000029424 vs real player 10102021301, waiting for real player result
[AutoChessScene_430500604870657] Bot vs real player battles will be handled by system timeout (62s)
[BattleService] Updated battle 430500604870657 state to StateBattleInProgress
[AutoChessScene_430500604870657] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R1)
[BattleStateManager_430500604870657] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] EndBattle uid: 10102021301, win: True
[AutoChessScene_430500604870657] Player 10102021301 sent EndBattleReq (win: True), instance: 430500604870657_2
[AutoChessScene_430500604870657] Auto EndBattle for bot 90000029424 vs real player 10102021301, bot result: False
[BattleInstance] 430500604870657_2 battle finished, winner: 10102021301, loser: 90000029424
[AutoChessScene_430500604870657] Battle instance 430500604870657_2 completed: Winner 10102021301, Loser 90000029424
[AutoChessScene_430500604870657] All battle instances finished, proceeding to settlement
[BattleStateManager_430500604870657] State: StateBattleInProgress -> StateRoundSettlement (R1, 3000ms)
[AutoChessScene_430500604870657] Processing battle results
[PlayerManager_430500604870657] Player 90000083579 health reduced by 1, current health: 2
[AutoChessScene_430500604870657] Player 90000083579 lost 1 health, winner: 90000098597
[AutoChessScene_430500604870657] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000098597, Loser: 90000083579
[PlayerManager_430500604870657] Player 90000029424 health reduced by 1, current health: 2
[AutoChessScene_430500604870657] Player 90000029424 lost 1 health, winner: 10102021301
[AutoChessScene_430500604870657] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10102021301, Loser: 90000029424
[BattleStateManager_430500604870657] Invalid state transition from StateRoundSettlement to StateRoundSettlement
[BattleService] Updated battle 430500604870657 state to StateRoundSettlement
[AutoChessScene_430500604870657] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R1)
[BattleStateManager_430500604870657] BattleStateChangedEvent published successfully
[AutoChessScene_430500604870657] Battle timeout occurred in state StateRoundSettlement
[AutoChessScene_430500604870657] Round settlement timeout - force confirm all players
[AutoChessScene_430500604870657] Force confirming round settlement for player 10102021301 due to timeout
[AutoChessScene_430500604870657] Player 10102021301 confirmed round settlement, count: 1
[AutoChessScene_430500604870657] Real player 10102021301 confirmed, auto-confirming all bots
[AutoChessScene_430500604870657] Auto-confirming round settlement for bot 90000098597
[AutoChessScene_430500604870657] Auto-confirming round settlement for bot 90000029424
[AutoChessScene_430500604870657] Auto-confirming round settlement for bot 90000083579
[AutoChessScene_430500604870657] All players confirmed round settlement, starting new round
[PlayerManager_430500604870657] Player 10102021301 ready status set to False
[PlayerManager_430500604870657] Player 90000098597 ready status set to False
[PlayerManager_430500604870657] Player 90000029424 ready status set to False
[PlayerManager_430500604870657] Player 90000083579 ready status set to False
[BattleStateManager_430500604870657] ===== STARTING NEW ROUND 2 =====
[BattleStateManager_430500604870657] Round 2 has buff selection: True
[BattleStateManager_430500604870657] Publishing RoundStartedEvent for round 2
[AutoChessScene_430500604870657] Round 2 started
[BattleStateManager_430500604870657] Setting state to StateRoundStart for round 2
[BattleStateManager_430500604870657] Invalid state transition from StateRoundSettlement to StateRoundStart
[BattleStateManager_430500604870657] ===== ROUND 2 INITIALIZATION COMPLETE =====
[AutoChessScene_430500604870657] Force confirming round settlement for player 90000098597 due to timeout
[AutoChessScene_430500604870657] Player 90000098597 confirmed round settlement, count: 1
[AutoChessScene_430500604870657] Still waiting for 3 players to confirm round settlement
[AutoChessScene_430500604870657] Force confirming round settlement for player 90000029424 due to timeout
[AutoChessScene_430500604870657] Player 90000029424 confirmed round settlement, count: 2
[AutoChessScene_430500604870657] Still waiting for 2 players to confirm round settlement
[AutoChessScene_430500604870657] Force confirming round settlement for player 90000083579 due to timeout
[AutoChessScene_430500604870657] Player 90000083579 confirmed round settlement, count: 3
[AutoChessScene_430500604870657] Still waiting for 1 players to confirm round settlement
[BattleStateManager_430500604870657] Published BattleTimeoutEvent for state StateRoundSettlement
[BattleStateManager_430500604870657] Round settlement timeout, waiting for AutoChessScene to handle next state
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021301 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021301 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_430500604870657] Player 10102021301 confirmed round settlement, count: 4
[AutoChessScene_430500604870657] Real player 10102021301 confirmed, auto-confirming all bots
[AutoChessScene_430500604870657] All players confirmed round settlement, starting new round
[PlayerManager_430500604870657] Player 10102021301 ready status set to False
[PlayerManager_430500604870657] Player 90000098597 ready status set to False
[PlayerManager_430500604870657] Player 90000029424 ready status set to False
[PlayerManager_430500604870657] Player 90000083579 ready status set to False
[BattleStateManager_430500604870657] ===== STARTING NEW ROUND 3 =====
[BattleStateManager_430500604870657] Round 3 has buff selection: False
[BattleStateManager_430500604870657] Publishing RoundStartedEvent for round 3
[AutoChessScene_430500604870657] Round 3 started
[BattleStateManager_430500604870657] Setting state to StateRoundStart for round 3
[BattleStateManager_430500604870657] Invalid state transition from StateRoundSettlement to StateRoundStart
[BattleStateManager_430500604870657] ===== ROUND 3 INITIALIZATION COMPLETE =====
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] LeaveBattle request received from player 10102021301
[BattleService] Player 10102021301 logout, cleaning up battle 430500604870657
[BattleService] Only one real player in battle 430500604870657, cleaning up entire battle
[BattleService] Starting cleanup for battle 430500604870657
[BattleService] Removed battle state for 430500604870657
[CheckerBoard_430500604870657] Cleared all entities
[BuffManager_430500604870657] Cleared all buffs
[AutoChessScene_430500604870657] Scene resources disposed
[SceneManager] Removed AutoChessScene 430500604870657 from thread management
[BattleService] Cleaned up scene for battle 430500604870657
[BattleService] Cleanup completed for battle 430500604870657
[BattleService] LeaveBattle completed successfully for player 10102021301
[NatsServer] Received publish message on subject: /30001/natsrpc.BattleService/LeaveBattle, no reply needed

Process finished with exit code -1,073,741,510.