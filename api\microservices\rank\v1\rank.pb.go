//GrpcAddressType:Rank
//GrpcServerType:server,world

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.1
// source: microservices/rank/v1/rank.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	v1 "liteframe/api/microservices/playerinfo/v1"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RankGroupType int32

const (
	RankGroupType_RoleRank  RankGroupType = 0
	RankGroupType_GuildRank RankGroupType = 1
	RankGroupType_TeamRank  RankGroupType = 2
	RankGroupType_PetRank   RankGroupType = 3
)

// Enum value maps for RankGroupType.
var (
	RankGroupType_name = map[int32]string{
		0: "RoleRank",
		1: "GuildRank",
		2: "TeamRank",
		3: "PetRank",
	}
	RankGroupType_value = map[string]int32{
		"RoleRank":  0,
		"GuildRank": 1,
		"TeamRank":  2,
		"PetRank":   3,
	}
)

func (x RankGroupType) Enum() *RankGroupType {
	p := new(RankGroupType)
	*p = x
	return p
}

func (x RankGroupType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RankGroupType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_rank_v1_rank_proto_enumTypes[0].Descriptor()
}

func (RankGroupType) Type() protoreflect.EnumType {
	return &file_microservices_rank_v1_rank_proto_enumTypes[0]
}

func (x RankGroupType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RankGroupType.Descriptor instead.
func (RankGroupType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{0}
}

// Type:Http
type RankType int32

const (
	RankType_RankTypeNone RankType = 0
	// 总评分
	RankType_FightPoint RankType = 101
	// 装备
	RankType_Equip RankType = 102
	// 等级
	RankType_Level RankType = 103
	// 职级or驭灵师等级
	RankType_MasterPath RankType = 104
	// 幻灵总评分
	RankType_Pet RankType = 201
	// 繁荣度(暂无)
	RankType_Guild RankType = 301
	// 公会等级排行
	RankType_GuildLevel RankType = 302
	// 公会捐献度排行
	RankType_GuildContribution RankType = 303
	// 战分（双生树积分）
	RankType_TTChasingScore RankType = 401
	// 混沌之塔
	RankType_HeroTrail RankType = 601
	// 梦魇之城
	RankType_SecretTerritory RankType = 605
)

// Enum value maps for RankType.
var (
	RankType_name = map[int32]string{
		0:   "RankTypeNone",
		101: "FightPoint",
		102: "Equip",
		103: "Level",
		104: "MasterPath",
		201: "Pet",
		301: "Guild",
		302: "GuildLevel",
		303: "GuildContribution",
		401: "TTChasingScore",
		601: "HeroTrail",
		605: "SecretTerritory",
	}
	RankType_value = map[string]int32{
		"RankTypeNone":      0,
		"FightPoint":        101,
		"Equip":             102,
		"Level":             103,
		"MasterPath":        104,
		"Pet":               201,
		"Guild":             301,
		"GuildLevel":        302,
		"GuildContribution": 303,
		"TTChasingScore":    401,
		"HeroTrail":         601,
		"SecretTerritory":   605,
	}
)

func (x RankType) Enum() *RankType {
	p := new(RankType)
	*p = x
	return p
}

func (x RankType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RankType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_rank_v1_rank_proto_enumTypes[1].Descriptor()
}

func (RankType) Type() protoreflect.EnumType {
	return &file_microservices_rank_v1_rank_proto_enumTypes[1]
}

func (x RankType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RankType.Descriptor instead.
func (RankType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{1}
}

type RankRoleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid       uint64 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`
	Zwid       int32  `protobuf:"varint,2,opt,name=zwid,proto3" json:"zwid,omitempty"`
	Kserver    int32  `protobuf:"varint,3,opt,name=kserver,proto3" json:"kserver,omitempty"`
	Name       string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Sex        int32  `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Level      int32  `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	FightPoint int64  `protobuf:"varint,7,opt,name=fightPoint,proto3" json:"fightPoint,omitempty"`
	GuildId    uint64 `protobuf:"varint,8,opt,name=guildId,proto3" json:"guildId,omitempty"`
}

func (x *RankRoleInfo) Reset() {
	*x = RankRoleInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RankRoleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankRoleInfo) ProtoMessage() {}

func (x *RankRoleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankRoleInfo.ProtoReflect.Descriptor instead.
func (*RankRoleInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{0}
}

func (x *RankRoleInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *RankRoleInfo) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *RankRoleInfo) GetKserver() int32 {
	if x != nil {
		return x.Kserver
	}
	return 0
}

func (x *RankRoleInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RankRoleInfo) GetSex() int32 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *RankRoleInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *RankRoleInfo) GetFightPoint() int64 {
	if x != nil {
		return x.FightPoint
	}
	return 0
}

func (x *RankRoleInfo) GetGuildId() uint64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

type SyncRoleInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info *RankRoleInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *SyncRoleInfoRequest) Reset() {
	*x = SyncRoleInfoRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncRoleInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncRoleInfoRequest) ProtoMessage() {}

func (x *SyncRoleInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncRoleInfoRequest.ProtoReflect.Descriptor instead.
func (*SyncRoleInfoRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{1}
}

func (x *SyncRoleInfoRequest) GetInfo() *RankRoleInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// The response message containing the message
type SyncRoleInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *SyncRoleInfoReply) Reset() {
	*x = SyncRoleInfoReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncRoleInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncRoleInfoReply) ProtoMessage() {}

func (x *SyncRoleInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncRoleInfoReply.ProtoReflect.Descriptor instead.
func (*SyncRoleInfoReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{2}
}

func (x *SyncRoleInfoReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type RankValueInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RankType  int32 `protobuf:"varint,1,opt,name=rankType,proto3" json:"rankType,omitempty"`
	RankValue int64 `protobuf:"varint,2,opt,name=rankValue,proto3" json:"rankValue,omitempty"`
}

func (x *RankValueInfo) Reset() {
	*x = RankValueInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RankValueInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankValueInfo) ProtoMessage() {}

func (x *RankValueInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankValueInfo.ProtoReflect.Descriptor instead.
func (*RankValueInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{3}
}

func (x *RankValueInfo) GetRankType() int32 {
	if x != nil {
		return x.RankType
	}
	return 0
}

func (x *RankValueInfo) GetRankValue() int64 {
	if x != nil {
		return x.RankValue
	}
	return 0
}

type UpdateRoleRankRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid    uint64           `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`
	Zwid    int32            `protobuf:"varint,2,opt,name=zwid,proto3" json:"zwid,omitempty"`
	Kserver int32            `protobuf:"varint,3,opt,name=kserver,proto3" json:"kserver,omitempty"`
	Info    []*RankValueInfo `protobuf:"bytes,4,rep,name=info,proto3" json:"info,omitempty"`
}

func (x *UpdateRoleRankRequest) Reset() {
	*x = UpdateRoleRankRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRoleRankRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleRankRequest) ProtoMessage() {}

func (x *UpdateRoleRankRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleRankRequest.ProtoReflect.Descriptor instead.
func (*UpdateRoleRankRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateRoleRankRequest) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *UpdateRoleRankRequest) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *UpdateRoleRankRequest) GetKserver() int32 {
	if x != nil {
		return x.Kserver
	}
	return 0
}

func (x *UpdateRoleRankRequest) GetInfo() []*RankValueInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type UpdateRoleRankReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UpdateRoleRankReply) Reset() {
	*x = UpdateRoleRankReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRoleRankReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleRankReply) ProtoMessage() {}

func (x *UpdateRoleRankReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleRankReply.ProtoReflect.Descriptor instead.
func (*UpdateRoleRankReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateRoleRankReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type RankGuildInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid        uint64 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`               // 公会ID
	Zwid        int32  `protobuf:"varint,2,opt,name=zwid,proto3" json:"zwid,omitempty"`               // 区服ID
	GuildName   string `protobuf:"bytes,3,opt,name=guildName,proto3" json:"guildName,omitempty"`      // 公会名称
	Level       int32  `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`             // 公会等级
	MasterGuid  uint64 `protobuf:"varint,5,opt,name=masterGuid,proto3" json:"masterGuid,omitempty"`   // 会长ID
	MemberCount int32  `protobuf:"varint,6,opt,name=memberCount,proto3" json:"memberCount,omitempty"` // 公会成员数量
	GuildIcon   string `protobuf:"bytes,7,opt,name=guildIcon,proto3" json:"guildIcon,omitempty"`      // 公会图标
	MasterName  string `protobuf:"bytes,8,opt,name=masterName,proto3" json:"masterName,omitempty"`    // 会长名字
	Notice      string `protobuf:"bytes,9,opt,name=notice,proto3" json:"notice,omitempty"`            // 公会宣言
}

func (x *RankGuildInfo) Reset() {
	*x = RankGuildInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RankGuildInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankGuildInfo) ProtoMessage() {}

func (x *RankGuildInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankGuildInfo.ProtoReflect.Descriptor instead.
func (*RankGuildInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{6}
}

func (x *RankGuildInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *RankGuildInfo) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *RankGuildInfo) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

func (x *RankGuildInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *RankGuildInfo) GetMasterGuid() uint64 {
	if x != nil {
		return x.MasterGuid
	}
	return 0
}

func (x *RankGuildInfo) GetMemberCount() int32 {
	if x != nil {
		return x.MemberCount
	}
	return 0
}

func (x *RankGuildInfo) GetGuildIcon() string {
	if x != nil {
		return x.GuildIcon
	}
	return ""
}

func (x *RankGuildInfo) GetMasterName() string {
	if x != nil {
		return x.MasterName
	}
	return ""
}

func (x *RankGuildInfo) GetNotice() string {
	if x != nil {
		return x.Notice
	}
	return ""
}

type SyncGuildInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info *RankGuildInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *SyncGuildInfoRequest) Reset() {
	*x = SyncGuildInfoRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncGuildInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncGuildInfoRequest) ProtoMessage() {}

func (x *SyncGuildInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncGuildInfoRequest.ProtoReflect.Descriptor instead.
func (*SyncGuildInfoRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{7}
}

func (x *SyncGuildInfoRequest) GetInfo() *RankGuildInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type SyncGuildInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *SyncGuildInfoReply) Reset() {
	*x = SyncGuildInfoReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncGuildInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncGuildInfoReply) ProtoMessage() {}

func (x *SyncGuildInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncGuildInfoReply.ProtoReflect.Descriptor instead.
func (*SyncGuildInfoReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{8}
}

func (x *SyncGuildInfoReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type UpdateGuildRankRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid    uint64           `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`
	Zwid    int32            `protobuf:"varint,2,opt,name=zwid,proto3" json:"zwid,omitempty"`
	Kserver int32            `protobuf:"varint,3,opt,name=kserver,proto3" json:"kserver,omitempty"`
	Info    []*RankValueInfo `protobuf:"bytes,4,rep,name=info,proto3" json:"info,omitempty"`
}

func (x *UpdateGuildRankRequest) Reset() {
	*x = UpdateGuildRankRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateGuildRankRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGuildRankRequest) ProtoMessage() {}

func (x *UpdateGuildRankRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGuildRankRequest.ProtoReflect.Descriptor instead.
func (*UpdateGuildRankRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateGuildRankRequest) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *UpdateGuildRankRequest) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *UpdateGuildRankRequest) GetKserver() int32 {
	if x != nil {
		return x.Kserver
	}
	return 0
}

func (x *UpdateGuildRankRequest) GetInfo() []*RankValueInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type UpdateGuildRankReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UpdateGuildRankReply) Reset() {
	*x = UpdateGuildRankReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateGuildRankReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGuildRankReply) ProtoMessage() {}

func (x *UpdateGuildRankReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGuildRankReply.ProtoReflect.Descriptor instead.
func (*UpdateGuildRankReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateGuildRankReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

// Type:Inner
type RankPetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GUID    uint64                `protobuf:"varint,1,opt,name=GUID,proto3" json:"GUID,omitempty"`
	OwnerId uint64                `protobuf:"varint,2,opt,name=OwnerId,proto3" json:"OwnerId,omitempty"`
	PetInfo *v1.PetInfoAttr_PISct `protobuf:"bytes,10,opt,name=PetInfo,proto3" json:"PetInfo,omitempty"`
}

func (x *RankPetInfo) Reset() {
	*x = RankPetInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RankPetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankPetInfo) ProtoMessage() {}

func (x *RankPetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankPetInfo.ProtoReflect.Descriptor instead.
func (*RankPetInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{11}
}

func (x *RankPetInfo) GetGUID() uint64 {
	if x != nil {
		return x.GUID
	}
	return 0
}

func (x *RankPetInfo) GetOwnerId() uint64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *RankPetInfo) GetPetInfo() *v1.PetInfoAttr_PISct {
	if x != nil {
		return x.PetInfo
	}
	return nil
}

// Type:Inner
type PetRankValueInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RankType  int32  `protobuf:"varint,1,opt,name=rankType,proto3" json:"rankType,omitempty"`
	RankValue int64  `protobuf:"varint,2,opt,name=rankValue,proto3" json:"rankValue,omitempty"`
	Guid      uint64 `protobuf:"varint,3,opt,name=guid,proto3" json:"guid,omitempty"`
}

func (x *PetRankValueInfo) Reset() {
	*x = PetRankValueInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetRankValueInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetRankValueInfo) ProtoMessage() {}

func (x *PetRankValueInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetRankValueInfo.ProtoReflect.Descriptor instead.
func (*PetRankValueInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{12}
}

func (x *PetRankValueInfo) GetRankType() int32 {
	if x != nil {
		return x.RankType
	}
	return 0
}

func (x *PetRankValueInfo) GetRankValue() int64 {
	if x != nil {
		return x.RankValue
	}
	return 0
}

func (x *PetRankValueInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

// Type:Inner
type RemoveInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid    uint64 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`
	Zwid    int32  `protobuf:"varint,2,opt,name=zwid,proto3" json:"zwid,omitempty"`
	Kserver int32  `protobuf:"varint,3,opt,name=kserver,proto3" json:"kserver,omitempty"`
}

func (x *RemoveInfo) Reset() {
	*x = RemoveInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveInfo) ProtoMessage() {}

func (x *RemoveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveInfo.ProtoReflect.Descriptor instead.
func (*RemoveInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{13}
}

func (x *RemoveInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *RemoveInfo) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *RemoveInfo) GetKserver() int32 {
	if x != nil {
		return x.Kserver
	}
	return 0
}

// Type:Inner
type PetRemoveInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PetID    int32       `protobuf:"varint,1,opt,name=PetID,proto3" json:"PetID,omitempty"`
	RankType int32       `protobuf:"varint,2,opt,name=rankType,proto3" json:"rankType,omitempty"`
	Pet      *RemoveInfo `protobuf:"bytes,3,opt,name=pet,proto3" json:"pet,omitempty"`
}

func (x *PetRemoveInfo) Reset() {
	*x = PetRemoveInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetRemoveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetRemoveInfo) ProtoMessage() {}

func (x *PetRemoveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetRemoveInfo.ProtoReflect.Descriptor instead.
func (*PetRemoveInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{14}
}

func (x *PetRemoveInfo) GetPetID() int32 {
	if x != nil {
		return x.PetID
	}
	return 0
}

func (x *PetRemoveInfo) GetRankType() int32 {
	if x != nil {
		return x.RankType
	}
	return 0
}

func (x *PetRemoveInfo) GetPet() *RemoveInfo {
	if x != nil {
		return x.Pet
	}
	return nil
}

// Type:Inner
// Target:S2W
type SyncPetRankInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RankInfo []*PetRankValueInfo `protobuf:"bytes,1,rep,name=rankInfo,proto3" json:"rankInfo,omitempty"`
	PetInfo  []*RankPetInfo      `protobuf:"bytes,2,rep,name=petInfo,proto3" json:"petInfo,omitempty"`
	PetRem   []*PetRemoveInfo    `protobuf:"bytes,3,rep,name=petRem,proto3" json:"petRem,omitempty"`
}

func (x *SyncPetRankInfo) Reset() {
	*x = SyncPetRankInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncPetRankInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPetRankInfo) ProtoMessage() {}

func (x *SyncPetRankInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPetRankInfo.ProtoReflect.Descriptor instead.
func (*SyncPetRankInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{15}
}

func (x *SyncPetRankInfo) GetRankInfo() []*PetRankValueInfo {
	if x != nil {
		return x.RankInfo
	}
	return nil
}

func (x *SyncPetRankInfo) GetPetInfo() []*RankPetInfo {
	if x != nil {
		return x.PetInfo
	}
	return nil
}

func (x *SyncPetRankInfo) GetPetRem() []*PetRemoveInfo {
	if x != nil {
		return x.PetRem
	}
	return nil
}

type SyncPetInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info []*RankPetInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
}

func (x *SyncPetInfoRequest) Reset() {
	*x = SyncPetInfoRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncPetInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPetInfoRequest) ProtoMessage() {}

func (x *SyncPetInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPetInfoRequest.ProtoReflect.Descriptor instead.
func (*SyncPetInfoRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{16}
}

func (x *SyncPetInfoRequest) GetInfo() []*RankPetInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type SyncPetInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *SyncPetInfoReply) Reset() {
	*x = SyncPetInfoReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncPetInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPetInfoReply) ProtoMessage() {}

func (x *SyncPetInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPetInfoReply.ProtoReflect.Descriptor instead.
func (*SyncPetInfoReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{17}
}

func (x *SyncPetInfoReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type UpdatePetRankRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Zwid    int32               `protobuf:"varint,2,opt,name=zwid,proto3" json:"zwid,omitempty"`
	Kserver int32               `protobuf:"varint,3,opt,name=kserver,proto3" json:"kserver,omitempty"`
	Info    []*PetRankValueInfo `protobuf:"bytes,4,rep,name=info,proto3" json:"info,omitempty"`
}

func (x *UpdatePetRankRequest) Reset() {
	*x = UpdatePetRankRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePetRankRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetRankRequest) ProtoMessage() {}

func (x *UpdatePetRankRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetRankRequest.ProtoReflect.Descriptor instead.
func (*UpdatePetRankRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{18}
}

func (x *UpdatePetRankRequest) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *UpdatePetRankRequest) GetKserver() int32 {
	if x != nil {
		return x.Kserver
	}
	return 0
}

func (x *UpdatePetRankRequest) GetInfo() []*PetRankValueInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type UpdatePetRankReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UpdatePetRankReply) Reset() {
	*x = UpdatePetRankReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePetRankReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetRankReply) ProtoMessage() {}

func (x *UpdatePetRankReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetRankReply.ProtoReflect.Descriptor instead.
func (*UpdatePetRankReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{19}
}

func (x *UpdatePetRankReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type RemoveRoleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Role *RemoveInfo `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *RemoveRoleRequest) Reset() {
	*x = RemoveRoleRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRoleRequest) ProtoMessage() {}

func (x *RemoveRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRoleRequest.ProtoReflect.Descriptor instead.
func (*RemoveRoleRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{20}
}

func (x *RemoveRoleRequest) GetRole() *RemoveInfo {
	if x != nil {
		return x.Role
	}
	return nil
}

type RemoveRoleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *RemoveRoleReply) Reset() {
	*x = RemoveRoleReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveRoleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRoleReply) ProtoMessage() {}

func (x *RemoveRoleReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRoleReply.ProtoReflect.Descriptor instead.
func (*RemoveRoleReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{21}
}

func (x *RemoveRoleReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type RemoveGuildRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guild *RemoveInfo `protobuf:"bytes,1,opt,name=guild,proto3" json:"guild,omitempty"`
}

func (x *RemoveGuildRequest) Reset() {
	*x = RemoveGuildRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveGuildRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveGuildRequest) ProtoMessage() {}

func (x *RemoveGuildRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveGuildRequest.ProtoReflect.Descriptor instead.
func (*RemoveGuildRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{22}
}

func (x *RemoveGuildRequest) GetGuild() *RemoveInfo {
	if x != nil {
		return x.Guild
	}
	return nil
}

type RemoveGuildReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *RemoveGuildReply) Reset() {
	*x = RemoveGuildReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveGuildReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveGuildReply) ProtoMessage() {}

func (x *RemoveGuildReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveGuildReply.ProtoReflect.Descriptor instead.
func (*RemoveGuildReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{23}
}

func (x *RemoveGuildReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type RemovePetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PetID    int32       `protobuf:"varint,1,opt,name=PetID,proto3" json:"PetID,omitempty"`
	RankType int32       `protobuf:"varint,2,opt,name=RankType,proto3" json:"RankType,omitempty"`
	Pet      *RemoveInfo `protobuf:"bytes,3,opt,name=pet,proto3" json:"pet,omitempty"`
}

func (x *RemovePetRequest) Reset() {
	*x = RemovePetRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemovePetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePetRequest) ProtoMessage() {}

func (x *RemovePetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePetRequest.ProtoReflect.Descriptor instead.
func (*RemovePetRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{24}
}

func (x *RemovePetRequest) GetPetID() int32 {
	if x != nil {
		return x.PetID
	}
	return 0
}

func (x *RemovePetRequest) GetRankType() int32 {
	if x != nil {
		return x.RankType
	}
	return 0
}

func (x *RemovePetRequest) GetPet() *RemoveInfo {
	if x != nil {
		return x.Pet
	}
	return nil
}

type RemovePetReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *RemovePetReply) Reset() {
	*x = RemovePetReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemovePetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePetReply) ProtoMessage() {}

func (x *RemovePetReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePetReply.ProtoReflect.Descriptor instead.
func (*RemovePetReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{25}
}

func (x *RemovePetReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type RemoveTwinsDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Zwid int32 `protobuf:"varint,1,opt,name=zwid,proto3" json:"zwid,omitempty"`
}

func (x *RemoveTwinsDataRequest) Reset() {
	*x = RemoveTwinsDataRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveTwinsDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveTwinsDataRequest) ProtoMessage() {}

func (x *RemoveTwinsDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveTwinsDataRequest.ProtoReflect.Descriptor instead.
func (*RemoveTwinsDataRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{26}
}

func (x *RemoveTwinsDataRequest) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

type RemoveTwinsDataReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	Zwid   int32 `protobuf:"varint,2,opt,name=zwid,proto3" json:"zwid,omitempty"`
}

func (x *RemoveTwinsDataReply) Reset() {
	*x = RemoveTwinsDataReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveTwinsDataReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveTwinsDataReply) ProtoMessage() {}

func (x *RemoveTwinsDataReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveTwinsDataReply.ProtoReflect.Descriptor instead.
func (*RemoveTwinsDataReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{27}
}

func (x *RemoveTwinsDataReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *RemoveTwinsDataReply) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

// Type:Http
type RoleRankInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid       uint64 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`
	Zwid       int32  `protobuf:"varint,2,opt,name=zwid,proto3" json:"zwid,omitempty"`
	Name       string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Sex        int32  `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Level      int32  `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
	FightPoint int64  `protobuf:"varint,6,opt,name=fightPoint,proto3" json:"fightPoint,omitempty"`
	Rank       int32  `protobuf:"varint,7,opt,name=rank,proto3" json:"rank,omitempty"`
	Tilte      int32  `protobuf:"varint,8,opt,name=tilte,proto3" json:"tilte,omitempty"`
	GuildName  string `protobuf:"bytes,9,opt,name=guildName,proto3" json:"guildName,omitempty"`
}

func (x *RoleRankInfo) Reset() {
	*x = RoleRankInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleRankInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleRankInfo) ProtoMessage() {}

func (x *RoleRankInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleRankInfo.ProtoReflect.Descriptor instead.
func (*RoleRankInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{28}
}

func (x *RoleRankInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *RoleRankInfo) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *RoleRankInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RoleRankInfo) GetSex() int32 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *RoleRankInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *RoleRankInfo) GetFightPoint() int64 {
	if x != nil {
		return x.FightPoint
	}
	return 0
}

func (x *RoleRankInfo) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *RoleRankInfo) GetTilte() int32 {
	if x != nil {
		return x.Tilte
	}
	return 0
}

func (x *RoleRankInfo) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

// Type:Http
type GuildRankInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid        uint64 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`               // 公会ID
	Zwid        int32  `protobuf:"varint,2,opt,name=zwid,proto3" json:"zwid,omitempty"`               // 区服ID
	GuildName   string `protobuf:"bytes,3,opt,name=guildName,proto3" json:"guildName,omitempty"`      // 公会名称
	Level       int32  `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`             // 公会等级
	MasterGuid  uint64 `protobuf:"varint,6,opt,name=masterGuid,proto3" json:"masterGuid,omitempty"`   // 会长ID
	Rank        int32  `protobuf:"varint,7,opt,name=rank,proto3" json:"rank,omitempty"`               // 排名
	MemberCount int32  `protobuf:"varint,8,opt,name=memberCount,proto3" json:"memberCount,omitempty"` // 公会成员数量
	GuildIcon   string `protobuf:"bytes,9,opt,name=guildIcon,proto3" json:"guildIcon,omitempty"`      // 公会图标
	MasterName  string `protobuf:"bytes,10,opt,name=masterName,proto3" json:"masterName,omitempty"`   // 会长名字
	Notice      string `protobuf:"bytes,11,opt,name=notice,proto3" json:"notice,omitempty"`           // 公会宣言
}

func (x *GuildRankInfo) Reset() {
	*x = GuildRankInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GuildRankInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuildRankInfo) ProtoMessage() {}

func (x *GuildRankInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuildRankInfo.ProtoReflect.Descriptor instead.
func (*GuildRankInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{29}
}

func (x *GuildRankInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *GuildRankInfo) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *GuildRankInfo) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

func (x *GuildRankInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *GuildRankInfo) GetMasterGuid() uint64 {
	if x != nil {
		return x.MasterGuid
	}
	return 0
}

func (x *GuildRankInfo) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *GuildRankInfo) GetMemberCount() int32 {
	if x != nil {
		return x.MemberCount
	}
	return 0
}

func (x *GuildRankInfo) GetGuildIcon() string {
	if x != nil {
		return x.GuildIcon
	}
	return ""
}

func (x *GuildRankInfo) GetMasterName() string {
	if x != nil {
		return x.MasterName
	}
	return ""
}

func (x *GuildRankInfo) GetNotice() string {
	if x != nil {
		return x.Notice
	}
	return ""
}

// Type:Http
type PetRankInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid    uint64 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`
	OwnerId uint64 `protobuf:"varint,2,opt,name=ownerId,proto3" json:"ownerId,omitempty"`
	Name    string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	PetID   int32  `protobuf:"varint,4,opt,name=petID,proto3" json:"petID,omitempty"`
	PetName string `protobuf:"bytes,5,opt,name=petName,proto3" json:"petName,omitempty"`
	Rank    int32  `protobuf:"varint,6,opt,name=rank,proto3" json:"rank,omitempty"`
}

func (x *PetRankInfo) Reset() {
	*x = PetRankInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetRankInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetRankInfo) ProtoMessage() {}

func (x *PetRankInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetRankInfo.ProtoReflect.Descriptor instead.
func (*PetRankInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{30}
}

func (x *PetRankInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *PetRankInfo) GetOwnerId() uint64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *PetRankInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PetRankInfo) GetPetID() int32 {
	if x != nil {
		return x.PetID
	}
	return 0
}

func (x *PetRankInfo) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *PetRankInfo) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

// Type:Http
type RankDataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Role       *RoleRankInfo  `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	Guild      *GuildRankInfo `protobuf:"bytes,2,opt,name=guild,proto3" json:"guild,omitempty"`
	Pet        *PetRankInfo   `protobuf:"bytes,3,opt,name=pet,proto3" json:"pet,omitempty"`
	RankValue  int64          `protobuf:"varint,10,opt,name=rankValue,proto3" json:"rankValue,omitempty"`
	RankValue2 int64          `protobuf:"varint,11,opt,name=rankValue2,proto3" json:"rankValue2,omitempty"`
}

func (x *RankDataInfo) Reset() {
	*x = RankDataInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RankDataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankDataInfo) ProtoMessage() {}

func (x *RankDataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankDataInfo.ProtoReflect.Descriptor instead.
func (*RankDataInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{31}
}

func (x *RankDataInfo) GetRole() *RoleRankInfo {
	if x != nil {
		return x.Role
	}
	return nil
}

func (x *RankDataInfo) GetGuild() *GuildRankInfo {
	if x != nil {
		return x.Guild
	}
	return nil
}

func (x *RankDataInfo) GetPet() *PetRankInfo {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *RankDataInfo) GetRankValue() int64 {
	if x != nil {
		return x.RankValue
	}
	return 0
}

func (x *RankDataInfo) GetRankValue2() int64 {
	if x != nil {
		return x.RankValue2
	}
	return 0
}

// Type:Http
type GetRankListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharGuid uint64 `protobuf:"varint,1,opt,name=charGuid,proto3" json:"charGuid,omitempty"`
	// 排行榜大类 0：角色 1：公会 2：队伍 3:单个幻灵
	RankGroup int32 `protobuf:"varint,3,opt,name=rankGroup,proto3" json:"rankGroup,omitempty"`
	// 排行榜类型
	RankType int32 `protobuf:"varint,4,opt,name=rankType,proto3" json:"rankType,omitempty"`
	// 三级页签 0：总榜 1：好友
	RankFlag int32 `protobuf:"varint,5,opt,name=rankFlag,proto3" json:"rankFlag,omitempty"`
	// 分页 0：全部  （总共100行&每页50行）
	RankPage int32 `protobuf:"varint,6,opt,name=rankPage,proto3" json:"rankPage,omitempty"`
}

func (x *GetRankListReq) Reset() {
	*x = GetRankListReq{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRankListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRankListReq) ProtoMessage() {}

func (x *GetRankListReq) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRankListReq.ProtoReflect.Descriptor instead.
func (*GetRankListReq) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{32}
}

func (x *GetRankListReq) GetCharGuid() uint64 {
	if x != nil {
		return x.CharGuid
	}
	return 0
}

func (x *GetRankListReq) GetRankGroup() int32 {
	if x != nil {
		return x.RankGroup
	}
	return 0
}

func (x *GetRankListReq) GetRankType() int32 {
	if x != nil {
		return x.RankType
	}
	return 0
}

func (x *GetRankListReq) GetRankFlag() int32 {
	if x != nil {
		return x.RankFlag
	}
	return 0
}

func (x *GetRankListReq) GetRankPage() int32 {
	if x != nil {
		return x.RankPage
	}
	return 0
}

// Type:Http
type GetRankListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharGuid  uint64          `protobuf:"varint,1,opt,name=charGuid,proto3" json:"charGuid,omitempty"`
	RankGroup int32           `protobuf:"varint,2,opt,name=rankGroup,proto3" json:"rankGroup,omitempty"`
	RankType  int32           `protobuf:"varint,3,opt,name=rankType,proto3" json:"rankType,omitempty"`
	RankFlag  int32           `protobuf:"varint,4,opt,name=rankFlag,proto3" json:"rankFlag,omitempty"`
	RankPage  int32           `protobuf:"varint,5,opt,name=rankPage,proto3" json:"rankPage,omitempty"`
	Data      []*RankDataInfo `protobuf:"bytes,10,rep,name=data,proto3" json:"data,omitempty"`
	SelfInfo  *RankDataInfo   `protobuf:"bytes,20,opt,name=selfInfo,proto3" json:"selfInfo,omitempty"`
}

func (x *GetRankListReply) Reset() {
	*x = GetRankListReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRankListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRankListReply) ProtoMessage() {}

func (x *GetRankListReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRankListReply.ProtoReflect.Descriptor instead.
func (*GetRankListReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{33}
}

func (x *GetRankListReply) GetCharGuid() uint64 {
	if x != nil {
		return x.CharGuid
	}
	return 0
}

func (x *GetRankListReply) GetRankGroup() int32 {
	if x != nil {
		return x.RankGroup
	}
	return 0
}

func (x *GetRankListReply) GetRankType() int32 {
	if x != nil {
		return x.RankType
	}
	return 0
}

func (x *GetRankListReply) GetRankFlag() int32 {
	if x != nil {
		return x.RankFlag
	}
	return 0
}

func (x *GetRankListReply) GetRankPage() int32 {
	if x != nil {
		return x.RankPage
	}
	return 0
}

func (x *GetRankListReply) GetData() []*RankDataInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetRankListReply) GetSelfInfo() *RankDataInfo {
	if x != nil {
		return x.SelfInfo
	}
	return nil
}

// Type:Http
type GetTwinsTreeRankListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharGuid uint64 `protobuf:"varint,1,opt,name=charGuid,proto3" json:"charGuid,omitempty"`
	// 分页 0：全部  （总共100行&每页50行）
	RankPage int32 `protobuf:"varint,2,opt,name=rankPage,proto3" json:"rankPage,omitempty"`
}

func (x *GetTwinsTreeRankListReq) Reset() {
	*x = GetTwinsTreeRankListReq{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTwinsTreeRankListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTwinsTreeRankListReq) ProtoMessage() {}

func (x *GetTwinsTreeRankListReq) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTwinsTreeRankListReq.ProtoReflect.Descriptor instead.
func (*GetTwinsTreeRankListReq) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{34}
}

func (x *GetTwinsTreeRankListReq) GetCharGuid() uint64 {
	if x != nil {
		return x.CharGuid
	}
	return 0
}

func (x *GetTwinsTreeRankListReq) GetRankPage() int32 {
	if x != nil {
		return x.RankPage
	}
	return 0
}

// Type:Http
type TwinsTreeRankInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharGuid  uint64 `protobuf:"varint,1,opt,name=charGuid,proto3" json:"charGuid,omitempty"`
	Zwid      int32  `protobuf:"varint,2,opt,name=zwid,proto3" json:"zwid,omitempty"`
	Name      string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Sex       int32  `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Icon      string `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty"`
	Rank      int32  `protobuf:"varint,10,opt,name=rank,proto3" json:"rank,omitempty"`
	RankValue int64  `protobuf:"varint,11,opt,name=rankValue,proto3" json:"rankValue,omitempty"`
}

func (x *TwinsTreeRankInfo) Reset() {
	*x = TwinsTreeRankInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TwinsTreeRankInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TwinsTreeRankInfo) ProtoMessage() {}

func (x *TwinsTreeRankInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TwinsTreeRankInfo.ProtoReflect.Descriptor instead.
func (*TwinsTreeRankInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{35}
}

func (x *TwinsTreeRankInfo) GetCharGuid() uint64 {
	if x != nil {
		return x.CharGuid
	}
	return 0
}

func (x *TwinsTreeRankInfo) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *TwinsTreeRankInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TwinsTreeRankInfo) GetSex() int32 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *TwinsTreeRankInfo) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *TwinsTreeRankInfo) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *TwinsTreeRankInfo) GetRankValue() int64 {
	if x != nil {
		return x.RankValue
	}
	return 0
}

// Type:Http
type GetTwinsTreeRankListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharGuid uint64               `protobuf:"varint,1,opt,name=charGuid,proto3" json:"charGuid,omitempty"`
	RankPage int32                `protobuf:"varint,2,opt,name=rankPage,proto3" json:"rankPage,omitempty"`
	Data     []*TwinsTreeRankInfo `protobuf:"bytes,10,rep,name=data,proto3" json:"data,omitempty"`
	SelfInfo *TwinsTreeRankInfo   `protobuf:"bytes,20,opt,name=selfInfo,proto3" json:"selfInfo,omitempty"`
}

func (x *GetTwinsTreeRankListReply) Reset() {
	*x = GetTwinsTreeRankListReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTwinsTreeRankListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTwinsTreeRankListReply) ProtoMessage() {}

func (x *GetTwinsTreeRankListReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTwinsTreeRankListReply.ProtoReflect.Descriptor instead.
func (*GetTwinsTreeRankListReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{36}
}

func (x *GetTwinsTreeRankListReply) GetCharGuid() uint64 {
	if x != nil {
		return x.CharGuid
	}
	return 0
}

func (x *GetTwinsTreeRankListReply) GetRankPage() int32 {
	if x != nil {
		return x.RankPage
	}
	return 0
}

func (x *GetTwinsTreeRankListReply) GetData() []*TwinsTreeRankInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetTwinsTreeRankListReply) GetSelfInfo() *TwinsTreeRankInfo {
	if x != nil {
		return x.SelfInfo
	}
	return nil
}

// Type:Http
type GetPetInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GUID uint64 `protobuf:"varint,1,opt,name=GUID,proto3" json:"GUID,omitempty"`
}

func (x *GetPetInfoReq) Reset() {
	*x = GetPetInfoReq{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPetInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetInfoReq) ProtoMessage() {}

func (x *GetPetInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetInfoReq.ProtoReflect.Descriptor instead.
func (*GetPetInfoReq) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{37}
}

func (x *GetPetInfoReq) GetGUID() uint64 {
	if x != nil {
		return x.GUID
	}
	return 0
}

// Type:Http
type GetPetInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GUID    uint64                `protobuf:"varint,1,opt,name=GUID,proto3" json:"GUID,omitempty"`
	Ret     int32                 `protobuf:"varint,2,opt,name=Ret,proto3" json:"Ret,omitempty"`
	OwnerId uint64                `protobuf:"varint,3,opt,name=OwnerId,proto3" json:"OwnerId,omitempty"`
	PetInfo *v1.PetInfoAttr_PISct `protobuf:"bytes,10,opt,name=PetInfo,proto3" json:"PetInfo,omitempty"`
}

func (x *GetPetInfoReply) Reset() {
	*x = GetPetInfoReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPetInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetInfoReply) ProtoMessage() {}

func (x *GetPetInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetInfoReply.ProtoReflect.Descriptor instead.
func (*GetPetInfoReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{38}
}

func (x *GetPetInfoReply) GetGUID() uint64 {
	if x != nil {
		return x.GUID
	}
	return 0
}

func (x *GetPetInfoReply) GetRet() int32 {
	if x != nil {
		return x.Ret
	}
	return 0
}

func (x *GetPetInfoReply) GetOwnerId() uint64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *GetPetInfoReply) GetPetInfo() *v1.PetInfoAttr_PISct {
	if x != nil {
		return x.PetInfo
	}
	return nil
}

type GuildAddMemberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharGuid  uint64 `protobuf:"varint,1,opt,name=charGuid,proto3" json:"charGuid,omitempty"`
	GuildGuid uint64 `protobuf:"varint,2,opt,name=guildGuid,proto3" json:"guildGuid,omitempty"`
}

func (x *GuildAddMemberRequest) Reset() {
	*x = GuildAddMemberRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GuildAddMemberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuildAddMemberRequest) ProtoMessage() {}

func (x *GuildAddMemberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuildAddMemberRequest.ProtoReflect.Descriptor instead.
func (*GuildAddMemberRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{39}
}

func (x *GuildAddMemberRequest) GetCharGuid() uint64 {
	if x != nil {
		return x.CharGuid
	}
	return 0
}

func (x *GuildAddMemberRequest) GetGuildGuid() uint64 {
	if x != nil {
		return x.GuildGuid
	}
	return 0
}

type GuildRemoveMemberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharGuid  uint64 `protobuf:"varint,1,opt,name=charGuid,proto3" json:"charGuid,omitempty"`
	GuildGuid uint64 `protobuf:"varint,2,opt,name=guildGuid,proto3" json:"guildGuid,omitempty"`
}

func (x *GuildRemoveMemberRequest) Reset() {
	*x = GuildRemoveMemberRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GuildRemoveMemberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuildRemoveMemberRequest) ProtoMessage() {}

func (x *GuildRemoveMemberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuildRemoveMemberRequest.ProtoReflect.Descriptor instead.
func (*GuildRemoveMemberRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{40}
}

func (x *GuildRemoveMemberRequest) GetCharGuid() uint64 {
	if x != nil {
		return x.CharGuid
	}
	return 0
}

func (x *GuildRemoveMemberRequest) GetGuildGuid() uint64 {
	if x != nil {
		return x.GuildGuid
	}
	return 0
}

type GuildMemberReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *GuildMemberReply) Reset() {
	*x = GuildMemberReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GuildMemberReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuildMemberReply) ProtoMessage() {}

func (x *GuildMemberReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuildMemberReply.ProtoReflect.Descriptor instead.
func (*GuildMemberReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{41}
}

func (x *GuildMemberReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type GuildGetAllMemberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildGuid uint64 `protobuf:"varint,1,opt,name=guildGuid,proto3" json:"guildGuid,omitempty"`
}

func (x *GuildGetAllMemberRequest) Reset() {
	*x = GuildGetAllMemberRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GuildGetAllMemberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuildGetAllMemberRequest) ProtoMessage() {}

func (x *GuildGetAllMemberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuildGetAllMemberRequest.ProtoReflect.Descriptor instead.
func (*GuildGetAllMemberRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{42}
}

func (x *GuildGetAllMemberRequest) GetGuildGuid() uint64 {
	if x != nil {
		return x.GuildGuid
	}
	return 0
}

type GuildGetAllMemberReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    int32    `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	GuildGuid uint64   `protobuf:"varint,2,opt,name=guildGuid,proto3" json:"guildGuid,omitempty"`
	Data      []uint64 `protobuf:"varint,3,rep,packed,name=data,proto3" json:"data,omitempty"`
}

func (x *GuildGetAllMemberReply) Reset() {
	*x = GuildGetAllMemberReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GuildGetAllMemberReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuildGetAllMemberReply) ProtoMessage() {}

func (x *GuildGetAllMemberReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuildGetAllMemberReply.ProtoReflect.Descriptor instead.
func (*GuildGetAllMemberReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{43}
}

func (x *GuildGetAllMemberReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GuildGetAllMemberReply) GetGuildGuid() uint64 {
	if x != nil {
		return x.GuildGuid
	}
	return 0
}

func (x *GuildGetAllMemberReply) GetData() []uint64 {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_microservices_rank_v1_rank_proto protoreflect.FileDescriptor

var file_microservices_rank_v1_rank_proto_rawDesc = []byte{
	0x0a, 0x20, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x72, 0x61, 0x6e, 0x6b, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x17, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x69,
	0x6e, 0x66, 0x6f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xc6, 0x01, 0x0a, 0x0c, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x6f, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x12, 0x0a, 0x04, 0x67, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04,
	0x67, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6b, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x78, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x65, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e,
	0x0a, 0x0a, 0x66, 0x69, 0x67, 0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x66, 0x69, 0x67, 0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63,
	0x52, 0x6f, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x39, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x6f, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x2b, 0x0a, 0x11, 0x53, 0x79,
	0x6e, 0x63, 0x52, 0x6f, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x49, 0x0a, 0x0d, 0x52, 0x61, 0x6e, 0x6b, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x67, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x7a, 0x77, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x3a,
	0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x2d, 0x0a, 0x13, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x83, 0x02, 0x0a, 0x0d, 0x52, 0x61,
	0x6e, 0x6b, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x67,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x7a,
	0x77, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65,
	0x72, 0x47, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x61, 0x73,
	0x74, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75, 0x69,
	0x6c, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x75,
	0x69, 0x6c, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x73,
	0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x22,
	0x52, 0x0a, 0x14, 0x53, 0x79, 0x6e, 0x63, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x52, 0x61, 0x6e, 0x6b, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x22, 0x2c, 0x0a, 0x12, 0x53, 0x79, 0x6e, 0x63, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x96, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x67, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x7a, 0x77, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x3a,
	0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x2e, 0x0a, 0x14, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x0b, 0x52,
	0x61, 0x6e, 0x6b, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x47, 0x55,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x47, 0x55, 0x49, 0x44, 0x12, 0x18,
	0x0a, 0x07, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x07, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x07, 0x50, 0x65, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x41, 0x75, 0x72, 0x6f,
	0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x74, 0x74, 0x72, 0x5f,
	0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x07, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x60,
	0x0a, 0x10, 0x50, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x67, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64,
	0x22, 0x4e, 0x0a, 0x0a, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x67, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x67, 0x75,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x22, 0x78, 0x0a, 0x0d, 0x50, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x14, 0x0a, 0x05, 0x50, 0x65, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x50, 0x65, 0x74, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x70, 0x65, 0x74, 0x22, 0xd8, 0x01, 0x0a, 0x0f, 0x53,
	0x79, 0x6e, 0x63, 0x50, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45,
	0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x52, 0x61,
	0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x72, 0x61, 0x6e,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x70, 0x65,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x50, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x70,
	0x65, 0x74, 0x52, 0x65, 0x6d, 0x22, 0x4e, 0x0a, 0x12, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x41, 0x75, 0x72, 0x6f,
	0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x2a, 0x0a, 0x10, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x83, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x52,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x6b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x6b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x2c, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x4c, 0x0a, 0x11, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x72,
	0x6f, 0x6c, 0x65, 0x22, 0x29, 0x0a, 0x0f, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x4f,
	0x0a, 0x12, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x05, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x22,
	0x2a, 0x0a, 0x10, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x7b, 0x0a, 0x10, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x50, 0x65, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x50, 0x65, 0x74, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x52, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x35, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x03, 0x70, 0x65, 0x74, 0x22, 0x28, 0x0a, 0x0e, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x2c, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x54, 0x77, 0x69, 0x6e,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x7a, 0x77, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x7a, 0x77, 0x69, 0x64,
	0x22, 0x42, 0x0a, 0x14, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x54, 0x77, 0x69, 0x6e, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x7a, 0x77, 0x69, 0x64, 0x22, 0xda, 0x01, 0x0a, 0x0c, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x61, 0x6e,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x73, 0x65, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x69, 0x67,
	0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x66,
	0x69, 0x67, 0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x6c, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x69,
	0x6c, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x97, 0x02, 0x0a, 0x0d, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67,
	0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72,
	0x61, 0x6e, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x63,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x0b,
	0x50, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x67,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e,
	0x6b, 0x22, 0xfd, 0x01, 0x0a, 0x0c, 0x52, 0x61, 0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x39, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52,
	0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x3c, 0x0a,
	0x05, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x61, 0x6e, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x12, 0x36, 0x0a, 0x03, 0x70,
	0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03,
	0x70, 0x65, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x32, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x32, 0x22, 0x9e, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61,
	0x6e, 0x6b, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61,
	0x6e, 0x6b, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x50, 0x61,
	0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x50, 0x61,
	0x67, 0x65, 0x22, 0x9e, 0x02, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47,
	0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x61, 0x6e, 0x6b, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x72, 0x61, 0x6e, 0x6b, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x6e,
	0x6b, 0x50, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x6e,
	0x6b, 0x50, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x61,
	0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x41, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x61, 0x6e,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x66, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x51, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x54, 0x77, 0x69, 0x6e, 0x73, 0x54,
	0x72, 0x65, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61,
	0x6e, 0x6b, 0x50, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61,
	0x6e, 0x6b, 0x50, 0x61, 0x67, 0x65, 0x22, 0xaf, 0x01, 0x0a, 0x11, 0x54, 0x77, 0x69, 0x6e, 0x73,
	0x54, 0x72, 0x65, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x63, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73,
	0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x61,
	0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72,
	0x61, 0x6e, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xdb, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x54, 0x77, 0x69, 0x6e, 0x73, 0x54, 0x72, 0x65, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75,
	0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x50, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x50, 0x61, 0x67, 0x65, 0x12, 0x3e,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x77, 0x69, 0x6e, 0x73, 0x54, 0x72, 0x65, 0x65,
	0x52, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x46,
	0x0a, 0x08, 0x73, 0x65, 0x6c, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x77, 0x69, 0x6e, 0x73,
	0x54, 0x72, 0x65, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x73, 0x65,
	0x6c, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x23, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x47, 0x55, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x47, 0x55, 0x49, 0x44, 0x22, 0x97, 0x01, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x47, 0x55, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x47,
	0x55, 0x49, 0x44, 0x12, 0x10, 0x0a, 0x03, 0x52, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x52, 0x65, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x44, 0x0a, 0x07, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x07, 0x50, 0x65,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x51, 0x0a, 0x15, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x41, 0x64,
	0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75,
	0x69, 0x6c, 0x64, 0x47, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x67,
	0x75, 0x69, 0x6c, 0x64, 0x47, 0x75, 0x69, 0x64, 0x22, 0x54, 0x0a, 0x18, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x47, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x47, 0x75, 0x69, 0x64, 0x22, 0x2a,
	0x0a, 0x10, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x38, 0x0a, 0x18, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x47,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64,
	0x47, 0x75, 0x69, 0x64, 0x22, 0x62, 0x0a, 0x16, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x47,
	0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x67, 0x75, 0x69, 0x6c, 0x64,
	0x47, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x2a, 0x47, 0x0a, 0x0d, 0x52, 0x61, 0x6e, 0x6b,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x61, 0x6e, 0x6b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x52, 0x61, 0x6e, 0x6b, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x61,
	0x6e, 0x6b, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x10,
	0x03, 0x2a, 0xcc, 0x01, 0x0a, 0x08, 0x52, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10,
	0x0a, 0x0c, 0x52, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00,
	0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x69, 0x67, 0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x10, 0x65,
	0x12, 0x09, 0x0a, 0x05, 0x45, 0x71, 0x75, 0x69, 0x70, 0x10, 0x66, 0x12, 0x09, 0x0a, 0x05, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x10, 0x67, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72,
	0x50, 0x61, 0x74, 0x68, 0x10, 0x68, 0x12, 0x08, 0x0a, 0x03, 0x50, 0x65, 0x74, 0x10, 0xc9, 0x01,
	0x12, 0x0a, 0x0a, 0x05, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x10, 0xad, 0x02, 0x12, 0x0f, 0x0a, 0x0a,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x10, 0xae, 0x02, 0x12, 0x16, 0x0a,
	0x11, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x10, 0xaf, 0x02, 0x12, 0x13, 0x0a, 0x0e, 0x54, 0x54, 0x43, 0x68, 0x61, 0x73, 0x69,
	0x6e, 0x67, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x10, 0x91, 0x03, 0x12, 0x0e, 0x0a, 0x09, 0x48, 0x65,
	0x72, 0x6f, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x10, 0xd9, 0x04, 0x12, 0x14, 0x0a, 0x0f, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x54, 0x65, 0x72, 0x72, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x10, 0xdd, 0x04,
	0x32, 0xba, 0x0b, 0x0a, 0x0b, 0x52, 0x61, 0x6e, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x6a, 0x0a, 0x0c, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x6f, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x2c, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x52,
	0x6f, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x6f, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x74, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x2e, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x00, 0x12, 0x64, 0x0a, 0x0a, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x12, 0x2a, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x2e, 0x41, 0x75, 0x72, 0x6f,
	0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x77, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f,
	0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47,
	0x75, 0x69, 0x6c, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2d, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x12, 0x67, 0x0a, 0x0b, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x12,
	0x2b, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x0b, 0x53, 0x79, 0x6e,
	0x63, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x53, 0x79, 0x6e, 0x63, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x00, 0x12, 0x71, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x52,
	0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x61, 0x0a, 0x09, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50,
	0x65, 0x74, 0x12, 0x29, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x65,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x77, 0x0a, 0x13, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x54, 0x77, 0x69, 0x6e, 0x73, 0x54, 0x72, 0x65, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x2f, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x54, 0x77, 0x69, 0x6e, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2d, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x54, 0x77, 0x69, 0x6e, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x00, 0x12, 0x6d, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x2e, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x41, 0x64, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x12, 0x73, 0x0a, 0x11, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x31, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x31, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x41, 0x75, 0x72,
	0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x32, 0xc7, 0x03,
	0x0a, 0x0c, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x74, 0x74, 0x70, 0x12, 0x8b,
	0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x27, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x41, 0x75,
	0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01,
	0x2a, 0x22, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xa2, 0x01, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x54, 0x77, 0x69, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x52, 0x61, 0x6e, 0x6b,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x30, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x77, 0x69, 0x6e, 0x73, 0x54, 0x72, 0x65, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x77, 0x69, 0x6e, 0x73, 0x54, 0x72, 0x65, 0x65, 0x52, 0x61, 0x6e,
	0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x61, 0x6e, 0x6b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x74, 0x77, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x65,
	0x65, 0x12, 0x83, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x26, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x70, 0x65, 0x74, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x22, 0x5a, 0x20, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_microservices_rank_v1_rank_proto_rawDescOnce sync.Once
	file_microservices_rank_v1_rank_proto_rawDescData = file_microservices_rank_v1_rank_proto_rawDesc
)

func file_microservices_rank_v1_rank_proto_rawDescGZIP() []byte {
	file_microservices_rank_v1_rank_proto_rawDescOnce.Do(func() {
		file_microservices_rank_v1_rank_proto_rawDescData = protoimpl.X.CompressGZIP(file_microservices_rank_v1_rank_proto_rawDescData)
	})
	return file_microservices_rank_v1_rank_proto_rawDescData
}

var file_microservices_rank_v1_rank_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_microservices_rank_v1_rank_proto_msgTypes = make([]protoimpl.MessageInfo, 44)
var file_microservices_rank_v1_rank_proto_goTypes = []any{
	(RankGroupType)(0),                // 0: Aurora.PlayerInfoServer.RankGroupType
	(RankType)(0),                     // 1: Aurora.PlayerInfoServer.RankType
	(*RankRoleInfo)(nil),              // 2: Aurora.PlayerInfoServer.RankRoleInfo
	(*SyncRoleInfoRequest)(nil),       // 3: Aurora.PlayerInfoServer.SyncRoleInfoRequest
	(*SyncRoleInfoReply)(nil),         // 4: Aurora.PlayerInfoServer.SyncRoleInfoReply
	(*RankValueInfo)(nil),             // 5: Aurora.PlayerInfoServer.RankValueInfo
	(*UpdateRoleRankRequest)(nil),     // 6: Aurora.PlayerInfoServer.UpdateRoleRankRequest
	(*UpdateRoleRankReply)(nil),       // 7: Aurora.PlayerInfoServer.UpdateRoleRankReply
	(*RankGuildInfo)(nil),             // 8: Aurora.PlayerInfoServer.RankGuildInfo
	(*SyncGuildInfoRequest)(nil),      // 9: Aurora.PlayerInfoServer.SyncGuildInfoRequest
	(*SyncGuildInfoReply)(nil),        // 10: Aurora.PlayerInfoServer.SyncGuildInfoReply
	(*UpdateGuildRankRequest)(nil),    // 11: Aurora.PlayerInfoServer.UpdateGuildRankRequest
	(*UpdateGuildRankReply)(nil),      // 12: Aurora.PlayerInfoServer.UpdateGuildRankReply
	(*RankPetInfo)(nil),               // 13: Aurora.PlayerInfoServer.RankPetInfo
	(*PetRankValueInfo)(nil),          // 14: Aurora.PlayerInfoServer.PetRankValueInfo
	(*RemoveInfo)(nil),                // 15: Aurora.PlayerInfoServer.RemoveInfo
	(*PetRemoveInfo)(nil),             // 16: Aurora.PlayerInfoServer.PetRemoveInfo
	(*SyncPetRankInfo)(nil),           // 17: Aurora.PlayerInfoServer.SyncPetRankInfo
	(*SyncPetInfoRequest)(nil),        // 18: Aurora.PlayerInfoServer.SyncPetInfoRequest
	(*SyncPetInfoReply)(nil),          // 19: Aurora.PlayerInfoServer.SyncPetInfoReply
	(*UpdatePetRankRequest)(nil),      // 20: Aurora.PlayerInfoServer.UpdatePetRankRequest
	(*UpdatePetRankReply)(nil),        // 21: Aurora.PlayerInfoServer.UpdatePetRankReply
	(*RemoveRoleRequest)(nil),         // 22: Aurora.PlayerInfoServer.RemoveRoleRequest
	(*RemoveRoleReply)(nil),           // 23: Aurora.PlayerInfoServer.RemoveRoleReply
	(*RemoveGuildRequest)(nil),        // 24: Aurora.PlayerInfoServer.RemoveGuildRequest
	(*RemoveGuildReply)(nil),          // 25: Aurora.PlayerInfoServer.RemoveGuildReply
	(*RemovePetRequest)(nil),          // 26: Aurora.PlayerInfoServer.RemovePetRequest
	(*RemovePetReply)(nil),            // 27: Aurora.PlayerInfoServer.RemovePetReply
	(*RemoveTwinsDataRequest)(nil),    // 28: Aurora.PlayerInfoServer.RemoveTwinsDataRequest
	(*RemoveTwinsDataReply)(nil),      // 29: Aurora.PlayerInfoServer.RemoveTwinsDataReply
	(*RoleRankInfo)(nil),              // 30: Aurora.PlayerInfoServer.RoleRankInfo
	(*GuildRankInfo)(nil),             // 31: Aurora.PlayerInfoServer.GuildRankInfo
	(*PetRankInfo)(nil),               // 32: Aurora.PlayerInfoServer.PetRankInfo
	(*RankDataInfo)(nil),              // 33: Aurora.PlayerInfoServer.RankDataInfo
	(*GetRankListReq)(nil),            // 34: Aurora.PlayerInfoServer.GetRankListReq
	(*GetRankListReply)(nil),          // 35: Aurora.PlayerInfoServer.GetRankListReply
	(*GetTwinsTreeRankListReq)(nil),   // 36: Aurora.PlayerInfoServer.GetTwinsTreeRankListReq
	(*TwinsTreeRankInfo)(nil),         // 37: Aurora.PlayerInfoServer.TwinsTreeRankInfo
	(*GetTwinsTreeRankListReply)(nil), // 38: Aurora.PlayerInfoServer.GetTwinsTreeRankListReply
	(*GetPetInfoReq)(nil),             // 39: Aurora.PlayerInfoServer.GetPetInfoReq
	(*GetPetInfoReply)(nil),           // 40: Aurora.PlayerInfoServer.GetPetInfoReply
	(*GuildAddMemberRequest)(nil),     // 41: Aurora.PlayerInfoServer.GuildAddMemberRequest
	(*GuildRemoveMemberRequest)(nil),  // 42: Aurora.PlayerInfoServer.GuildRemoveMemberRequest
	(*GuildMemberReply)(nil),          // 43: Aurora.PlayerInfoServer.GuildMemberReply
	(*GuildGetAllMemberRequest)(nil),  // 44: Aurora.PlayerInfoServer.GuildGetAllMemberRequest
	(*GuildGetAllMemberReply)(nil),    // 45: Aurora.PlayerInfoServer.GuildGetAllMemberReply
	(*v1.PetInfoAttr_PISct)(nil),      // 46: Aurora.PlayerInfoServer.PetInfoAttr_PISct
}
var file_microservices_rank_v1_rank_proto_depIdxs = []int32{
	2,  // 0: Aurora.PlayerInfoServer.SyncRoleInfoRequest.info:type_name -> Aurora.PlayerInfoServer.RankRoleInfo
	5,  // 1: Aurora.PlayerInfoServer.UpdateRoleRankRequest.info:type_name -> Aurora.PlayerInfoServer.RankValueInfo
	8,  // 2: Aurora.PlayerInfoServer.SyncGuildInfoRequest.info:type_name -> Aurora.PlayerInfoServer.RankGuildInfo
	5,  // 3: Aurora.PlayerInfoServer.UpdateGuildRankRequest.info:type_name -> Aurora.PlayerInfoServer.RankValueInfo
	46, // 4: Aurora.PlayerInfoServer.RankPetInfo.PetInfo:type_name -> Aurora.PlayerInfoServer.PetInfoAttr_PISct
	15, // 5: Aurora.PlayerInfoServer.PetRemoveInfo.pet:type_name -> Aurora.PlayerInfoServer.RemoveInfo
	14, // 6: Aurora.PlayerInfoServer.SyncPetRankInfo.rankInfo:type_name -> Aurora.PlayerInfoServer.PetRankValueInfo
	13, // 7: Aurora.PlayerInfoServer.SyncPetRankInfo.petInfo:type_name -> Aurora.PlayerInfoServer.RankPetInfo
	16, // 8: Aurora.PlayerInfoServer.SyncPetRankInfo.petRem:type_name -> Aurora.PlayerInfoServer.PetRemoveInfo
	13, // 9: Aurora.PlayerInfoServer.SyncPetInfoRequest.info:type_name -> Aurora.PlayerInfoServer.RankPetInfo
	14, // 10: Aurora.PlayerInfoServer.UpdatePetRankRequest.info:type_name -> Aurora.PlayerInfoServer.PetRankValueInfo
	15, // 11: Aurora.PlayerInfoServer.RemoveRoleRequest.role:type_name -> Aurora.PlayerInfoServer.RemoveInfo
	15, // 12: Aurora.PlayerInfoServer.RemoveGuildRequest.guild:type_name -> Aurora.PlayerInfoServer.RemoveInfo
	15, // 13: Aurora.PlayerInfoServer.RemovePetRequest.pet:type_name -> Aurora.PlayerInfoServer.RemoveInfo
	30, // 14: Aurora.PlayerInfoServer.RankDataInfo.role:type_name -> Aurora.PlayerInfoServer.RoleRankInfo
	31, // 15: Aurora.PlayerInfoServer.RankDataInfo.guild:type_name -> Aurora.PlayerInfoServer.GuildRankInfo
	32, // 16: Aurora.PlayerInfoServer.RankDataInfo.pet:type_name -> Aurora.PlayerInfoServer.PetRankInfo
	33, // 17: Aurora.PlayerInfoServer.GetRankListReply.data:type_name -> Aurora.PlayerInfoServer.RankDataInfo
	33, // 18: Aurora.PlayerInfoServer.GetRankListReply.selfInfo:type_name -> Aurora.PlayerInfoServer.RankDataInfo
	37, // 19: Aurora.PlayerInfoServer.GetTwinsTreeRankListReply.data:type_name -> Aurora.PlayerInfoServer.TwinsTreeRankInfo
	37, // 20: Aurora.PlayerInfoServer.GetTwinsTreeRankListReply.selfInfo:type_name -> Aurora.PlayerInfoServer.TwinsTreeRankInfo
	46, // 21: Aurora.PlayerInfoServer.GetPetInfoReply.PetInfo:type_name -> Aurora.PlayerInfoServer.PetInfoAttr_PISct
	3,  // 22: Aurora.PlayerInfoServer.RankService.SyncRoleInfo:input_type -> Aurora.PlayerInfoServer.SyncRoleInfoRequest
	6,  // 23: Aurora.PlayerInfoServer.RankService.UpdateRoleRankInfo:input_type -> Aurora.PlayerInfoServer.UpdateRoleRankRequest
	22, // 24: Aurora.PlayerInfoServer.RankService.RemoveRole:input_type -> Aurora.PlayerInfoServer.RemoveRoleRequest
	9,  // 25: Aurora.PlayerInfoServer.RankService.SyncGuildInfo:input_type -> Aurora.PlayerInfoServer.SyncGuildInfoRequest
	11, // 26: Aurora.PlayerInfoServer.RankService.UpdateGuildRankInfo:input_type -> Aurora.PlayerInfoServer.UpdateGuildRankRequest
	24, // 27: Aurora.PlayerInfoServer.RankService.RemoveGuild:input_type -> Aurora.PlayerInfoServer.RemoveGuildRequest
	18, // 28: Aurora.PlayerInfoServer.RankService.SyncPetInfo:input_type -> Aurora.PlayerInfoServer.SyncPetInfoRequest
	20, // 29: Aurora.PlayerInfoServer.RankService.UpdatePetRankInfo:input_type -> Aurora.PlayerInfoServer.UpdatePetRankRequest
	26, // 30: Aurora.PlayerInfoServer.RankService.RemovePet:input_type -> Aurora.PlayerInfoServer.RemovePetRequest
	28, // 31: Aurora.PlayerInfoServer.RankService.RemoveTwinsTreeData:input_type -> Aurora.PlayerInfoServer.RemoveTwinsDataRequest
	41, // 32: Aurora.PlayerInfoServer.RankService.AddGuildMember:input_type -> Aurora.PlayerInfoServer.GuildAddMemberRequest
	42, // 33: Aurora.PlayerInfoServer.RankService.RemoveGuildMember:input_type -> Aurora.PlayerInfoServer.GuildRemoveMemberRequest
	44, // 34: Aurora.PlayerInfoServer.RankService.GetGuildMember:input_type -> Aurora.PlayerInfoServer.GuildGetAllMemberRequest
	34, // 35: Aurora.PlayerInfoServer.RankListHttp.GetRankListData:input_type -> Aurora.PlayerInfoServer.GetRankListReq
	36, // 36: Aurora.PlayerInfoServer.RankListHttp.GetTwinTreeRankList:input_type -> Aurora.PlayerInfoServer.GetTwinsTreeRankListReq
	39, // 37: Aurora.PlayerInfoServer.RankListHttp.GetPetInfo:input_type -> Aurora.PlayerInfoServer.GetPetInfoReq
	4,  // 38: Aurora.PlayerInfoServer.RankService.SyncRoleInfo:output_type -> Aurora.PlayerInfoServer.SyncRoleInfoReply
	7,  // 39: Aurora.PlayerInfoServer.RankService.UpdateRoleRankInfo:output_type -> Aurora.PlayerInfoServer.UpdateRoleRankReply
	23, // 40: Aurora.PlayerInfoServer.RankService.RemoveRole:output_type -> Aurora.PlayerInfoServer.RemoveRoleReply
	10, // 41: Aurora.PlayerInfoServer.RankService.SyncGuildInfo:output_type -> Aurora.PlayerInfoServer.SyncGuildInfoReply
	12, // 42: Aurora.PlayerInfoServer.RankService.UpdateGuildRankInfo:output_type -> Aurora.PlayerInfoServer.UpdateGuildRankReply
	25, // 43: Aurora.PlayerInfoServer.RankService.RemoveGuild:output_type -> Aurora.PlayerInfoServer.RemoveGuildReply
	19, // 44: Aurora.PlayerInfoServer.RankService.SyncPetInfo:output_type -> Aurora.PlayerInfoServer.SyncPetInfoReply
	21, // 45: Aurora.PlayerInfoServer.RankService.UpdatePetRankInfo:output_type -> Aurora.PlayerInfoServer.UpdatePetRankReply
	27, // 46: Aurora.PlayerInfoServer.RankService.RemovePet:output_type -> Aurora.PlayerInfoServer.RemovePetReply
	29, // 47: Aurora.PlayerInfoServer.RankService.RemoveTwinsTreeData:output_type -> Aurora.PlayerInfoServer.RemoveTwinsDataReply
	43, // 48: Aurora.PlayerInfoServer.RankService.AddGuildMember:output_type -> Aurora.PlayerInfoServer.GuildMemberReply
	43, // 49: Aurora.PlayerInfoServer.RankService.RemoveGuildMember:output_type -> Aurora.PlayerInfoServer.GuildMemberReply
	45, // 50: Aurora.PlayerInfoServer.RankService.GetGuildMember:output_type -> Aurora.PlayerInfoServer.GuildGetAllMemberReply
	35, // 51: Aurora.PlayerInfoServer.RankListHttp.GetRankListData:output_type -> Aurora.PlayerInfoServer.GetRankListReply
	38, // 52: Aurora.PlayerInfoServer.RankListHttp.GetTwinTreeRankList:output_type -> Aurora.PlayerInfoServer.GetTwinsTreeRankListReply
	40, // 53: Aurora.PlayerInfoServer.RankListHttp.GetPetInfo:output_type -> Aurora.PlayerInfoServer.GetPetInfoReply
	38, // [38:54] is the sub-list for method output_type
	22, // [22:38] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_microservices_rank_v1_rank_proto_init() }
func file_microservices_rank_v1_rank_proto_init() {
	if File_microservices_rank_v1_rank_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_microservices_rank_v1_rank_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   44,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_microservices_rank_v1_rank_proto_goTypes,
		DependencyIndexes: file_microservices_rank_v1_rank_proto_depIdxs,
		EnumInfos:         file_microservices_rank_v1_rank_proto_enumTypes,
		MessageInfos:      file_microservices_rank_v1_rank_proto_msgTypes,
	}.Build()
	File_microservices_rank_v1_rank_proto = out.File
	file_microservices_rank_v1_rank_proto_rawDesc = nil
	file_microservices_rank_v1_rank_proto_goTypes = nil
	file_microservices_rank_v1_rank_proto_depIdxs = nil
}
