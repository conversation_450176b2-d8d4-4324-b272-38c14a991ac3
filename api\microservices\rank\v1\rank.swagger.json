{"swagger": "2.0", "info": {"title": "microservices/rank/v1/rank.proto", "version": "version not set"}, "tags": [{"name": "RankService"}, {"name": "RankListHttp"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/rankservice/petinfo": {"post": {"operationId": "RankListHttp_GetPetInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetPetInfoReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetPetInfoReq"}}], "tags": ["RankListHttp"]}}, "/api/rankservice/ranklist": {"post": {"operationId": "RankListHttp_GetRankListData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetRankListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetRankListReq"}}], "tags": ["RankListHttp"]}}, "/api/rankservice/twinstree": {"post": {"operationId": "RankListHttp_GetTwinTreeRankList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetTwinsTreeRankListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetTwinsTreeRankListReq"}}], "tags": ["RankListHttp"]}}}, "definitions": {"PlayerInfoServerGetPetInfoReply": {"type": "object", "properties": {"GUID": {"type": "string", "format": "uint64"}, "Ret": {"type": "integer", "format": "int32"}, "OwnerId": {"type": "string", "format": "uint64"}, "PetInfo": {"$ref": "#/definitions/PlayerInfoServerPetInfoAttr_PISct"}}, "title": "Type:Http"}, "PlayerInfoServerGetPetInfoReq": {"type": "object", "properties": {"GUID": {"type": "string", "format": "uint64"}}, "title": "Type:Http"}, "PlayerInfoServerGetRankListReply": {"type": "object", "properties": {"charGuid": {"type": "string", "format": "uint64"}, "rankGroup": {"type": "integer", "format": "int32"}, "rankType": {"type": "integer", "format": "int32"}, "rankFlag": {"type": "integer", "format": "int32"}, "rankPage": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerRankDataInfo"}}, "selfInfo": {"$ref": "#/definitions/PlayerInfoServerRankDataInfo"}}, "title": "Type:Http"}, "PlayerInfoServerGetRankListReq": {"type": "object", "properties": {"charGuid": {"type": "string", "format": "uint64"}, "rankGroup": {"type": "integer", "format": "int32", "title": "排行榜大类 0：角色 1：公会 2：队伍 3:单个幻灵"}, "rankType": {"type": "integer", "format": "int32", "title": "排行榜类型"}, "rankFlag": {"type": "integer", "format": "int32", "title": "三级页签 0：总榜 1：好友"}, "rankPage": {"type": "integer", "format": "int32", "title": "分页 0：全部  （总共100行&每页50行）"}}, "title": "Type:Http"}, "PlayerInfoServerGetTwinsTreeRankListReply": {"type": "object", "properties": {"charGuid": {"type": "string", "format": "uint64"}, "rankPage": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerTwinsTreeRankInfo"}}, "selfInfo": {"$ref": "#/definitions/PlayerInfoServerTwinsTreeRankInfo"}}, "title": "Type:Http"}, "PlayerInfoServerGetTwinsTreeRankListReq": {"type": "object", "properties": {"charGuid": {"type": "string", "format": "uint64"}, "rankPage": {"type": "integer", "format": "int32", "title": "分页 0：全部  （总共100行&每页50行）"}}, "title": "Type:Http"}, "PlayerInfoServerGuildGetAllMemberReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "guildGuid": {"type": "string", "format": "uint64"}, "data": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "PlayerInfoServerGuildMemberReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerGuildRankInfo": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64", "title": "公会ID"}, "zwid": {"type": "integer", "format": "int32", "title": "区服ID"}, "guildName": {"type": "string", "title": "公会名称"}, "level": {"type": "integer", "format": "int32", "title": "公会等级"}, "masterGuid": {"type": "string", "format": "uint64", "title": "会长ID"}, "rank": {"type": "integer", "format": "int32", "title": "排名"}, "memberCount": {"type": "integer", "format": "int32", "title": "公会成员数量"}, "guildIcon": {"type": "string", "title": "公会图标"}, "masterName": {"type": "string", "title": "会长名字"}, "notice": {"type": "string", "title": "公会宣言"}}, "title": "Type:Http"}, "PlayerInfoServerPetFlairInfo_PISct": {"type": "object", "properties": {"AllAddValue": {"type": "integer", "format": "int32"}, "BaseValue": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetGeniusInfo_PISct": {"type": "object", "properties": {"Amend": {"type": "number", "format": "float"}, "ID": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetInfoAttr_PISct": {"type": "object", "properties": {"BasicAttrList": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "CombatModelID": {"type": "integer", "format": "int32"}, "EvolveLevel": {"type": "integer", "format": "int32"}, "FightPoint": {"type": "integer", "format": "int32"}, "Flair": {"type": "object", "additionalProperties": {"$ref": "#/definitions/PlayerInfoServerPetFlairInfo_PISct"}}, "GeniusCount": {"type": "integer", "format": "int32"}, "GeniusList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerPetGeniusInfo_PISct"}}, "GUID": {"type": "string", "format": "uint64"}, "GWTHRate": {"type": "number", "format": "float"}, "IsFightPet": {"type": "integer", "format": "int32"}, "IsLocked": {"type": "boolean"}, "Level": {"type": "integer", "format": "int32"}, "LevelEXP": {"type": "string", "format": "int64"}, "Name": {"type": "string"}, "PetID": {"type": "integer", "format": "int32"}, "Quality": {"type": "integer", "format": "int32"}, "SkillBookList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerPetSkillBookInfo_PISct"}}, "StarLevel": {"type": "integer", "format": "int32"}, "XDValueAGI": {"type": "integer", "format": "int32"}, "XDValueCON": {"type": "integer", "format": "int32"}, "XDValueINT": {"type": "integer", "format": "int32"}, "XDValueSTR": {"type": "integer", "format": "int32"}, "XianDan": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetRankInfo": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64"}, "ownerId": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "petID": {"type": "integer", "format": "int32"}, "petName": {"type": "string"}, "rank": {"type": "integer", "format": "int32"}}, "title": "Type:Http"}, "PlayerInfoServerPetRankValueInfo": {"type": "object", "properties": {"rankType": {"type": "integer", "format": "int32"}, "rankValue": {"type": "string", "format": "int64"}, "guid": {"type": "string", "format": "uint64"}}, "title": "Type:Inner"}, "PlayerInfoServerPetSkillBookInfo_PISct": {"type": "object", "properties": {"Level": {"type": "integer", "format": "int32"}, "SkillBookID": {"type": "integer", "format": "int32"}, "Slot": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerRankDataInfo": {"type": "object", "properties": {"role": {"$ref": "#/definitions/PlayerInfoServerRoleRankInfo"}, "guild": {"$ref": "#/definitions/PlayerInfoServerGuildRankInfo"}, "pet": {"$ref": "#/definitions/PlayerInfoServerPetRankInfo"}, "rankValue": {"type": "string", "format": "int64"}, "rankValue2": {"type": "string", "format": "int64"}}, "title": "Type:Http"}, "PlayerInfoServerRankGuildInfo": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64", "title": "公会ID"}, "zwid": {"type": "integer", "format": "int32", "title": "区服ID"}, "guildName": {"type": "string", "title": "公会名称"}, "level": {"type": "integer", "format": "int32", "title": "公会等级"}, "masterGuid": {"type": "string", "format": "uint64", "title": "会长ID"}, "memberCount": {"type": "integer", "format": "int32", "title": "公会成员数量"}, "guildIcon": {"type": "string", "title": "公会图标"}, "masterName": {"type": "string", "title": "会长名字"}, "notice": {"type": "string", "title": "公会宣言"}}}, "PlayerInfoServerRankPetInfo": {"type": "object", "properties": {"GUID": {"type": "string", "format": "uint64"}, "OwnerId": {"type": "string", "format": "uint64"}, "PetInfo": {"$ref": "#/definitions/PlayerInfoServerPetInfoAttr_PISct"}}, "title": "Type:Inner"}, "PlayerInfoServerRankRoleInfo": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64"}, "zwid": {"type": "integer", "format": "int32"}, "kserver": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "sex": {"type": "integer", "format": "int32"}, "level": {"type": "integer", "format": "int32"}, "fightPoint": {"type": "string", "format": "int64"}, "guildId": {"type": "string", "format": "uint64"}}}, "PlayerInfoServerRankValueInfo": {"type": "object", "properties": {"rankType": {"type": "integer", "format": "int32"}, "rankValue": {"type": "string", "format": "int64"}}}, "PlayerInfoServerRemoveGuildReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerRemoveInfo": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64"}, "zwid": {"type": "integer", "format": "int32"}, "kserver": {"type": "integer", "format": "int32"}}, "title": "Type:Inner"}, "PlayerInfoServerRemovePetReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerRemoveRoleReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerRemoveTwinsDataReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "zwid": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerRoleRankInfo": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64"}, "zwid": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "sex": {"type": "integer", "format": "int32"}, "level": {"type": "integer", "format": "int32"}, "fightPoint": {"type": "string", "format": "int64"}, "rank": {"type": "integer", "format": "int32"}, "tilte": {"type": "integer", "format": "int32"}, "guildName": {"type": "string"}}, "title": "Type:Http"}, "PlayerInfoServerSyncGuildInfoReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerSyncPetInfoReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerSyncRoleInfoReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}, "title": "The response message containing the message"}, "PlayerInfoServerTwinsTreeRankInfo": {"type": "object", "properties": {"charGuid": {"type": "string", "format": "uint64"}, "zwid": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "sex": {"type": "integer", "format": "int32"}, "icon": {"type": "string"}, "rank": {"type": "integer", "format": "int32"}, "rankValue": {"type": "string", "format": "int64"}}, "title": "Type:Http"}, "PlayerInfoServerUpdateGuildRankReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerUpdatePetRankReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerUpdateRoleRankReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}