using BattleServer.Framework;
using BattleServer.Nats.SerializerRegistry;
using LiteFrame.Framework;
using System.Net;
using Game.Core;
using BattleServer.Game.AutoChess;
using System.Collections.Concurrent;

namespace BattleServer.Service;

public partial class BattleService
{
    // 使用线程安全的ConcurrentDictionary替代Dictionary
    private static readonly ConcurrentDictionary<long, BattleState> _battles = new ConcurrentDictionary<long, BattleState>();
    private static readonly ConcurrentDictionary<long, AutoChessScene> _sceneManager = new ConcurrentDictionary<long, AutoChessScene>();
    private static readonly ConcurrentDictionary<long, long> _playerToBattle = new ConcurrentDictionary<long, long>(); // 玩家ID到战斗ID的映射

    // 战斗玩家进入状态跟踪
    private static readonly ConcurrentDictionary<long, HashSet<long>> _battlePlayerEntered = new ConcurrentDictionary<long, HashSet<long>>(); // 战斗ID -> 已进入的玩家ID集合
    private static readonly ConcurrentDictionary<long, List<long>> _battleAllPlayers = new ConcurrentDictionary<long, List<long>>(); // 战斗ID -> 所有玩家ID列表
    private static readonly ConcurrentDictionary<long, long> _battleCreateTime = new ConcurrentDictionary<long, long>(); // 战斗ID -> 创建时间戳

    // 线程安全的battleId生成器
    private static long _battleIdCounter = 0;

    // 超时配置（毫秒）
    private const int ENTER_BATTLE_TIMEOUT_MS = 10000; // 10秒超时（测试环境缩短）

    public CreateBattleResp CreateBattle(CreateBattleReq request)
    {
        try
        {
            var serverIdStr = Program.NatsServer.ServerId;
            var serverId = long.Parse(serverIdStr);

            // 生成唯一的battleId：时间戳 + 递增计数器
            var battleId = (Environment.TickCount64 << 16) + Interlocked.Increment(ref _battleIdCounter);

            // 验证请求数据
            if (request.CreateInfo == null)
            {
                Log.Error("[BattleService] CreateBattle: CreateInfo is null");
                return new CreateBattleResp { ServerId = serverId, BattleId = 0 };
            }

            var players = request.CreateInfo.Players;
            var teams = request.CreateInfo.Teams;

            if (players == null || players.Count != 4)
            {
                Log.Error($"[BattleService] CreateBattle: Invalid player count {players?.Count ?? 0}, expected 4");
                return new CreateBattleResp { ServerId = serverId, BattleId = 0 };
            }

            if (teams == null || teams.Count != 4)
            {
                Log.Error($"[BattleService] CreateBattle: Invalid team count {teams?.Count ?? 0}, expected 4");
                return new CreateBattleResp { ServerId = serverId, BattleId = 0 };
            }

            // 检查battleId是否重复
            if (!_battles.TryAdd(battleId, BattleState.StateNone))
            {
                Log.Warning($"[BattleService] CreateBattle: Duplicate battleId {battleId}");
                return new CreateBattleResp { ServerId = serverId, BattleId = 0 };
            }

            // 提取玩家数据和阵容数据
            var playerIds = new List<long>();
            var playerLineups = new Dictionary<long, List<int>>();
            var playerHeroInfos = new Dictionary<long, List<PBBattleHeroInfo>>(); // 保存完整英雄信息
            var playerServerIds = new Dictionary<long, string>();
            var playerBasicInfos = new Dictionary<long, (string name, int level, int trophy)>(); // 新增：保存玩家基本信息

            for (int i = 0; i < players.Count; i++)
            {
                var player = players[i];
                var team = teams[i];
                var playerId = (long)player.Uid;

                playerIds.Add(playerId);

                // 从PBBattleTeamInfo中提取英雄阵容
                var lineup = new List<int>();
                var heroInfos = new List<PBBattleHeroInfo>(); // 保存完整英雄信息
                if (team?.Heros != null)
                {
                    foreach (var hero in team.Heros)
                    {
                        lineup.Add(hero.Id);
                        // 保存完整的英雄信息（包括Level和AwakeLevel）
                        heroInfos.Add(new PBBattleHeroInfo
                        {
                            Id = hero.Id,
                            Level = hero.Level,
                            StarLevel = 1, // 战斗开始时所有英雄都是1星
                            AwakeLevel = hero.AwakeLevel
                        });
                    }
                }

                playerLineups[playerId] = lineup;
                playerHeroInfos[playerId] = heroInfos; // 保存完整英雄信息

                // 提取玩家服务器ID
                playerServerIds[playerId] = player.ServerId ?? "unknown";

                // 提取玩家基本信息（名称、等级、奖杯）
                playerBasicInfos[playerId] = (
                    name: player.Name ?? $"Player{playerId}",
                    level: player.Level,
                    trophy: player.Throphy
                );

                // 建立玩家到战斗的映射（线程安全）
                _playerToBattle.TryAdd(playerId, battleId);

                Log.Info($"[BattleService] Player {playerId} ({player.Name}) Level {player.Level} Trophy {player.Throphy} from server {player.ServerId} lineup: [{string.Join(", ", lineup)}]");
            }

            // 创建AutoChessScene但不启动状态机
            var scene = new AutoChessScene(serverIdStr);
            scene.InitBattleWithoutStart(battleId, playerIds, playerLineups, playerHeroInfos, playerServerIds, playerBasicInfos); // 传递完整英雄信息和玩家基本信息

            // 原子化添加到字典 - 如果battleId已存在则失败
            if (!_sceneManager.TryAdd(battleId, scene))
            {
                scene.Dispose();
                Log.Error($"[BattleService] Battle ID {battleId} already exists");
                return new CreateBattleResp { ServerId = long.Parse(serverIdStr), BattleId = 0 };
            }

            // 战斗状态已在上面设置为StateNone，无需重复添加

            // 初始化玩家进入状态跟踪
            _battlePlayerEntered.TryAdd(battleId, new HashSet<long>());
            _battleAllPlayers.TryAdd(battleId, new List<long>(playerIds));
            _battleCreateTime.TryAdd(battleId, Environment.TickCount64);

            // 将Scene添加到SceneManager进行线程管理
            SceneManager.Instance.AddAutoChessScene(scene);

            Log.Info($"[BattleService] Battle {battleId} created successfully with {playerIds.Count} players");
            Log.Info($"[BattleService] Battle {battleId} is now waiting for all players to call EnterBattle RPC");
            Log.Info($"[BattleService] Current battle state: {BattleState.StateNone} (waiting for player entry)");
            Log.Info($"[BattleService] Players expected to enter: [{string.Join(", ", playerIds)}]");
            Log.Info($"[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====");
            Log.Info($"[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====");
            Log.Info($"[BattleService] ===== BOT STRATEGY: Player {playerIds[0]} is real player, others are bots =====");

            // 立即让机器人进入战斗，不等待超时
            AutoEnterBotsImmediately(battleId, playerIds);

            return new CreateBattleResp { ServerId = serverId, BattleId = battleId };
        }
        catch (Exception ex)
        {
            Log.Error($"[BattleService] CreateBattle error: {ex.Message}");
            return new CreateBattleResp { ServerId = long.Parse(Program.NatsServer.ServerId), BattleId = 0 };
        }
    }

    public EnterBattleResp EnterBattle(EnterBattleReq request)
    {
        try
        {
            var playerId = (long)request.Uid;
            Log.Info($"[BattleService] ===== RECEIVED EnterBattle RPC =====");
            Log.Info($"[BattleService] Player {playerId} is calling EnterBattle");
            Log.Info($"[BattleService] NATS communication is working correctly!");

            // 查找玩家所在的战斗
            if (!_playerToBattle.TryGetValue(playerId, out long battleId))
            {
                Log.Warning($"[BattleService] Player {playerId} not found in any battle for EnterBattle");
                return new EnterBattleResp { Code = -1 };
            }

            // 获取战斗场景
            if (!_sceneManager.TryGetValue(battleId, out var scene))
            {
                Log.Error($"[BattleService] Battle scene {battleId} not found for player {playerId}");
                return new EnterBattleResp { Code = -2 };
            }

            // 获取玩家进入状态跟踪
            if (!_battlePlayerEntered.TryGetValue(battleId, out var enteredPlayers))
            {
                Log.Error($"[BattleService] Battle {battleId} player tracking not found");
                return new EnterBattleResp { Code = -3 };
            }

            // 获取当前战斗状态
            var currentBattleState = _battles.GetValueOrDefault(battleId, BattleState.StateNone);

            // 检查玩家是否已经进入过
            lock (enteredPlayers)
            {
                // 如果是第一次进入（StateNone状态）
                if (currentBattleState == BattleState.StateNone)
                {
                    if (enteredPlayers.Contains(playerId))
                    {
                        Log.Warning($"[BattleService] Player {playerId} already entered battle {battleId} in initial phase");
                        return new EnterBattleResp { Code = 0 }; // 重复进入不算错误
                    }

                    // 标记玩家已进入
                    enteredPlayers.Add(playerId);
                    Log.Info($"[BattleService] Player {playerId} entered battle {battleId} (initial), current count: {enteredPlayers.Count}");

                    // 检查是否所有玩家都已进入
                    if (_battleAllPlayers.TryGetValue(battleId, out var allPlayers) &&
                        enteredPlayers.Count >= allPlayers.Count)
                    {
                        // 所有玩家都已进入，启动战斗状态机
                        Log.Info($"[BattleService] All {allPlayers.Count} players entered battle {battleId}, starting battle state machine");
                        Log.Info($"[BattleService] Entered players: [{string.Join(", ", enteredPlayers)}]");
                        Log.Info($"[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle {battleId}");

                        scene.StartBattleStateMachine();

                        // 更新战斗状态
                        var updateSuccess = _battles.TryUpdate(battleId, BattleState.StateRoundStart, BattleState.StateNone);
                        Log.Info($"[BattleService] Battle state update result: {updateSuccess}, new state: {BattleState.StateRoundStart}");
                    }
                    else
                    {
                        var totalPlayers = allPlayers?.Count ?? 0;
                        Log.Info($"[BattleService] Waiting for more players to enter battle {battleId}: {enteredPlayers.Count}/{totalPlayers}");
                        if (allPlayers != null)
                        {
                            var waitingPlayers = allPlayers.Where(p => !enteredPlayers.Contains(p)).ToList();
                            Log.Info($"[BattleService] Still waiting for players: [{string.Join(", ", waitingPlayers)}]");
                        }
                    }
                }
                else
                {
                    // 如果是回合确认（非StateNone状态），通知Scene处理新回合确认
                    Log.Info($"[BattleService] Player {playerId} confirmed round settlement, current state: {currentBattleState}");
                    scene.HandleRoundConfirmation(playerId);
                }
            }

            return new EnterBattleResp { Code = 0 };
        }
        catch (Exception ex)
        {
            Log.Error($"[BattleService] EnterBattle error: {ex.Message}");
            return new EnterBattleResp { Code = -99 };
        }
    }
    public SelectBufferResp SelectBuffer(SelectBufferReq request)
    {
        try
        {
            var scene = GetSceneByPlayerId(request.Uid);
            if (scene != null)
            {
                return scene.SelectBuff((long)request.Uid, (int)request.BufferID);
            }

            Log.Warning($"[BattleService] No battle found for player {request.Uid} in SelectBuffer");
            return new SelectBufferResp { Code = -1 };
        }
        catch (Exception ex)
        {
            Log.Error($"[BattleService] SelectBuffer error: {ex.Message}");
            return new SelectBufferResp { Code = -2 };
        }
    }

    public MergeHeroResp MergeHero(MergeHeroReq request)
    {
        try
        {
            var scene = GetSceneByPlayerId(request.Uid);
            if (scene != null)
            {
                // 传递moves参数到AutoChessScene进行处理
                return scene.MergeHero((long)request.Uid, request.From, request.To, request.Moves);
            }

            Log.Warning($"[BattleService] No battle found for player {request.Uid} in MergeHero");
            return new MergeHeroResp { Code = -1, From = request.From, To = request.To, NewHero = null };
        }
        catch (Exception ex)
        {
            Log.Error($"[BattleService] MergeHero error: {ex.Message}");
            return new MergeHeroResp { Code = -2, From = request.From, To = request.To, NewHero = null };
        }
    }
    public ReadyBattleResp BattleReady(ReadyBattleReq request)
    {
        try
        {
            var scene = GetSceneByPlayerId(request.Uid);
            if (scene != null)
            {
                // 先处理移动操作，再设置准备状态
                scene.SetPlayerReady((long)request.Uid, true, request.Moves);
                return new ReadyBattleResp { Code = 0 };
            }

            Log.Warning($"[BattleService] No battle found for player {request.Uid} in BattleReady");
            return new ReadyBattleResp { Code = -1 };
        }
        catch (Exception ex)
        {
            Log.Error($"[BattleService] BattleReady error: {ex.Message}");
            return new ReadyBattleResp { Code = -2 };
        }
    }
    public EndBattleResp EndBattle(EndBattleReq request)
    {
        try
        {
            var uid = request.Uid;
            var win = request.Win;

            Log.Info($"[BattleService] EndBattle uid: {uid}, win: {win}");

            var scene = GetSceneByPlayerId(uid);
            if (scene != null)
            {
                var (success, _) = scene.HandleBattleEnd(uid, win);
                return new EndBattleResp { Code = success ? 0 : -1 };
            }

            Log.Warning($"[BattleService] No battle found for player {uid}");
            return new EndBattleResp { Code = -1 };
        }
        catch (Exception ex)
        {
            Log.Error($"[BattleService] EndBattle error: {ex.Message}");
            return new EndBattleResp { Code = -2 };
        }
    }

    /// <summary>
    /// 玩家离开战斗 - RPC接口实现
    /// </summary>
    public LeaveBattleResp LeaveBattle(LeaveBattleReq request)
    {
        try
        {
            var uid = (long)request.Uid;
            Log.Info($"[BattleService] LeaveBattle request received from player {uid}");

            // 验证请求参数
            if (uid <= 0)
            {
                Log.Warning($"[BattleService] Invalid player ID in LeaveBattle request: {uid}");
                return new LeaveBattleResp { Code = -2 }; // 参数错误
            }

            // 检查玩家是否在战斗中
            if (!_playerToBattle.ContainsKey(uid))
            {
                Log.Info($"[BattleService] Player {uid} not in any battle, LeaveBattle request ignored");
                return new LeaveBattleResp { Code = 0 }; // 成功（玩家本来就不在战斗中）
            }

            // 调用清理逻辑
            CleanupBattleByPlayerId(uid);

            Log.Info($"[BattleService] LeaveBattle completed successfully for player {uid}");
            return new LeaveBattleResp { Code = 0 }; // 成功
        }
        catch (Exception ex)
        {
            Log.Error($"[BattleService] LeaveBattle error for player {request?.Uid}: {ex.Message}");
            Log.Error($"[BattleService] LeaveBattle stack trace: {ex.StackTrace}");
            return new LeaveBattleResp { Code = -1 }; // 失败
        }
    }

    /// <summary>
    /// 根据玩家ID获取对应的战斗场景
    /// </summary>
    private AutoChessScene GetSceneByPlayerId(ulong playerId)
    {
        var playerIdLong = (long)playerId;

        if (_playerToBattle.TryGetValue(playerIdLong, out long battleId))
        {
            return _sceneManager.GetValueOrDefault(battleId);
        }

        Log.Warning($"[BattleService] Player {playerId} not found in any battle");
        return null;
    }

    /// <summary>
    /// 更新战斗状态 - 由AutoChessScene调用
    /// </summary>
    public static void UpdateBattleState(long battleId, BattleState newState)
    {
        if (_battles.ContainsKey(battleId))
        {
            _battles[battleId] = newState;
            Log.Info($"[BattleService] Updated battle {battleId} state to {newState}");
        }
        else
        {
            Log.Warning($"[BattleService] Cannot update state for unknown battle {battleId}");
        }
    }

    /// <summary>
    /// 获取战斗当前状态
    /// </summary>
    public static BattleState GetBattleState(long battleId)
    {
        return _battles.GetValueOrDefault(battleId, BattleState.StateNone);
    }

    /// <summary>
    /// 获取玩家当前所在的战斗ID
    /// </summary>
    public static long GetPlayerBattleId(long playerId)
    {
        return _playerToBattle.GetValueOrDefault(playerId, 0);
    }

    /// <summary>
    /// 检查玩家是否在战斗中
    /// </summary>
    public static bool IsPlayerInBattle(long playerId)
    {
        return _playerToBattle.ContainsKey(playerId);
    }

    /// <summary>
    /// 根据玩家ID清理战斗 - 用于玩家登出时清理
    /// </summary>
    public static void CleanupBattleByPlayerId(long playerId)
    {
        try
        {
            // 查找玩家所在的战斗
            if (_playerToBattle.TryGetValue(playerId, out long battleId))
            {
                Log.Info($"[BattleService] Player {playerId} logout, cleaning up battle {battleId}");

                // 获取战斗中的所有玩家
                if (_sceneManager.TryGetValue(battleId, out var scene))
                {
                    var allPlayerIds = scene.GetAllPlayerIds();
                    var realPlayerCount = allPlayerIds.Count(id => !id.ToString().StartsWith("9"));

                    // 如果只有一个真实玩家，直接清理整个战斗
                    if (realPlayerCount <= 1)
                    {
                        Log.Info($"[BattleService] Only one real player in battle {battleId}, cleaning up entire battle");
                        CleanupBattle(battleId);
                    }
                    else
                    {
                        // 如果有多个真实玩家，只移除当前玩家的映射
                        _playerToBattle.TryRemove(playerId, out _);
                        Log.Info($"[BattleService] Removed player {playerId} from battle {battleId}, battle continues with other players");
                    }
                }
                else
                {
                    // 战斗场景不存在，只清理玩家映射
                    _playerToBattle.TryRemove(playerId, out _);
                    Log.Info($"[BattleService] Battle scene {battleId} not found, only removed player mapping");
                }
            }
            else
            {
                Log.Info($"[BattleService] Player {playerId} not in any battle, no cleanup needed");
            }
        }
        catch (Exception ex)
        {
            Log.Error($"[BattleService] Error cleaning up battle for player {playerId}: {ex.Message}");
        }
    }

    /// <summary>
    /// 清理已结束的战斗
    /// </summary>
    public static void CleanupBattle(long battleId)
    {
        Log.Info($"[BattleService] Starting cleanup for battle {battleId}");

        // 线程安全地移除战斗状态
        if (_battles.TryRemove(battleId, out _))
        {
            Log.Info($"[BattleService] Removed battle state for {battleId}");
        }

        // 清理玩家进入状态跟踪
        _battlePlayerEntered.TryRemove(battleId, out _);
        _battleAllPlayers.TryRemove(battleId, out _);
        _battleCreateTime.TryRemove(battleId, out _);

        // 线程安全地移除Scene
        if (_sceneManager.TryRemove(battleId, out var scene))
        {
            // 清理玩家映射
            var playerIds = scene.GetAllPlayerIds();
            foreach (var playerId in playerIds)
            {
                _playerToBattle.TryRemove(playerId, out _);
            }

            // 从SceneManager移除Scene（会自动Dispose）
            SceneManager.Instance.RemoveAutoChessScene(battleId);

            Log.Info($"[BattleService] Cleaned up scene for battle {battleId}");
        }
        else
        {
            Log.Warning($"[BattleService] Scene for battle {battleId} not found during cleanup");
        }

        Log.Info($"[BattleService] Cleanup completed for battle {battleId}");
    }

    /// <summary>
    /// 检查并处理EnterBattle超时的战斗
    /// 应该由定时器定期调用
    /// </summary>
    public static void CheckEnterBattleTimeouts()
    {
        var currentTime = Environment.TickCount64;
        var timeoutBattles = new List<long>();

        // 只在有活跃战斗时输出日志
        if (_battles.Count == 0) return;

        // 查找超时的战斗
        foreach (var kvp in _battleCreateTime)
        {
            var battleId = kvp.Key;
            var createTime = kvp.Value;
            var elapsedTime = currentTime - createTime;

            // 检查是否超时
            if (elapsedTime > ENTER_BATTLE_TIMEOUT_MS)
            {
                // 检查是否还在等待玩家进入
                if (_battles.TryGetValue(battleId, out var state))
                {
                    if (state == BattleState.StateNone)
                    {
                        Log.Warning($"[BattleService] Battle {battleId} timed out after {elapsedTime}ms, adding to timeout list");
                        timeoutBattles.Add(battleId);
                    }
                    // 移除冗余的非StateNone状态日志
                }
                else
                {
                    Log.Warning($"[BattleService] Battle {battleId} not found in battles dictionary");
                }
            }
            else
            {
                // 简化的等待状态记录（不输出日志，避免刷屏）
            }
        }

        // 处理超时的战斗
        foreach (var battleId in timeoutBattles)
        {
            HandleEnterBattleTimeout(battleId);
        }

        // 移除无用的日志输出
    }

    /// <summary>
    /// 立即让机器人进入战斗
    /// </summary>
    private static void AutoEnterBotsImmediately(long battleId, List<long> playerIds)
    {
        try
        {
            if (!_battlePlayerEntered.TryGetValue(battleId, out var enteredPlayers))
            {
                Log.Error($"[BattleService] Cannot find entered players tracking for battle {battleId}");
                return;
            }

            lock (enteredPlayers)
            {
                // 让所有机器人（UID以9开头）立即进入
                foreach (var playerId in playerIds)
                {
                    if (playerId.ToString().StartsWith("9") && !enteredPlayers.Contains(playerId))
                    {
                        enteredPlayers.Add(playerId);
                        Log.Info($"[BattleService] Auto-entered bot player {playerId} immediately");
                    }
                }

                Log.Info($"[BattleService] Battle {battleId} bots auto-entered: {enteredPlayers.Count}/{playerIds.Count} players ready");

                // 检查是否所有玩家都已进入（如果只有机器人的话）
                if (enteredPlayers.Count >= playerIds.Count)
                {
                    if (_sceneManager.TryGetValue(battleId, out var scene))
                    {
                        Log.Info($"[BattleService] All players entered immediately, starting battle {battleId}");
                        scene.StartBattleStateMachine();
                        var updateSuccess = _battles.TryUpdate(battleId, BattleState.StateRoundStart, BattleState.StateNone);
                        Log.Info($"[BattleService] Battle state update result: {updateSuccess}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Log.Error($"[BattleService] Error auto-entering bots for battle {battleId}: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理EnterBattle超时的战斗
    /// </summary>
    private static void HandleEnterBattleTimeout(long battleId)
    {
        try
        {
            Log.Warning($"[BattleService] Battle {battleId} EnterBattle timeout, forcing start or cleanup");

            // 获取已进入和所有玩家信息
            if (!_battlePlayerEntered.TryGetValue(battleId, out var enteredPlayers) ||
                !_battleAllPlayers.TryGetValue(battleId, out var allPlayers))
            {
                Log.Error($"[BattleService] Cannot find player data for timeout battle {battleId}");
                return;
            }

            // 获取战斗场景
            if (!_sceneManager.TryGetValue(battleId, out var scene))
            {
                Log.Error($"[BattleService] Cannot find scene for timeout battle {battleId}");
                CleanupBattle(battleId);
                return;
            }

            lock (enteredPlayers)
            {
                var enteredCount = enteredPlayers.Count;
                var totalCount = allPlayers.Count;

                Log.Info($"[BattleService] Battle {battleId} timeout: {enteredCount}/{totalCount} players entered");

                // 测试环境：强制让所有玩家进入（包括机器人）
                Log.Info($"[BattleService] Force starting battle {battleId} with {enteredCount} players (timeout handling)");
                Log.Info($"[BattleService] Test environment: Auto-entering all players including bots");

                // 自动让所有玩家进入（测试环境处理）
                foreach (var playerId in allPlayers)
                {
                    if (!enteredPlayers.Contains(playerId))
                    {
                        Log.Info($"[BattleService] Auto-entering player {playerId} due to timeout");
                        enteredPlayers.Add(playerId);
                    }
                }

                // 启动战斗状态机
                Log.Info($"[BattleService] Calling scene.StartBattleStateMachine() for battle {battleId}");
                scene.StartBattleStateMachine();

                var updateSuccess = _battles.TryUpdate(battleId, BattleState.StateRoundStart, BattleState.StateNone);
                Log.Info($"[BattleService] Battle state update result: {updateSuccess} for battle {battleId}");
            }
        }
        catch (Exception ex)
        {
            Log.Error($"[BattleService] Error handling EnterBattle timeout for battle {battleId}: {ex.Message}");
            CleanupBattle(battleId);
        }
    }

    /// <summary>
    /// 调试方法：打印当前系统状态
    /// </summary>
    public static void PrintSystemStatus()
    {
        Log.Info($"[BattleService] ===== SYSTEM STATUS DEBUG =====");
        Log.Info($"[BattleService] Active battles: {_battles.Count}");
        Log.Info($"[BattleService] Active scenes: {_sceneManager.Count}");
        Log.Info($"[BattleService] Player mappings: {_playerToBattle.Count}");

        foreach (var kvp in _battles)
        {
            var battleId = kvp.Key;
            var state = kvp.Value;

            Log.Info($"[BattleService] Battle {battleId}: State={state}");

            if (_battlePlayerEntered.TryGetValue(battleId, out var enteredPlayers) &&
                _battleAllPlayers.TryGetValue(battleId, out var allPlayers))
            {
                Log.Info($"[BattleService] Battle {battleId}: Players entered {enteredPlayers.Count}/{allPlayers.Count}");
                Log.Info($"[BattleService] Battle {battleId}: Expected players: [{string.Join(", ", allPlayers)}]");
                Log.Info($"[BattleService] Battle {battleId}: Entered players: [{string.Join(", ", enteredPlayers)}]");

                var waitingPlayers = allPlayers.Where(p => !enteredPlayers.Contains(p)).ToList();
                if (waitingPlayers.Count > 0)
                {
                    Log.Info($"[BattleService] Battle {battleId}: Still waiting for: [{string.Join(", ", waitingPlayers)}]");
                }
            }

            if (_battleCreateTime.TryGetValue(battleId, out var createTime))
            {
                var elapsedTime = Environment.TickCount64 - createTime;
                Log.Info($"[BattleService] Battle {battleId}: Created {elapsedTime}ms ago");
            }
        }

        Log.Info($"[BattleService] ===== END SYSTEM STATUS =====");
    }



    /// <summary>
    /// 简化的系统状态监控（一行日志）
    /// </summary>
    public static void PrintSimpleStatus()
    {
        // 如果没有活跃战斗，不输出日志
        if (_battles.Count == 0) return;

        var waitingBattles = 0;
        var activeBattles = 0;
        var totalPlayers = 0;
        var enteredPlayers = 0;

        foreach (var kvp in _battles)
        {
            var battleId = kvp.Key;
            var state = kvp.Value;

            if (state == BattleState.StateNone)
            {
                waitingBattles++;
                if (_battlePlayerEntered.TryGetValue(battleId, out var entered) &&
                    _battleAllPlayers.TryGetValue(battleId, out var all))
                {
                    totalPlayers += all.Count;
                    enteredPlayers += entered.Count;
                }
            }
            else
            {
                activeBattles++;
            }
        }

        if (waitingBattles > 0 || activeBattles > 0)
        {
            Log.Info($"[BattleService] Status: {waitingBattles} waiting ({enteredPlayers}/{totalPlayers} entered), {activeBattles} active");
        }
    }


}