//GrpcAddressType:Rank
//GrpcServerType:server,world

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.1
// source: microservices/rank/v1/rank.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RankService_SyncRoleInfo_FullMethodName        = "/Aurora.PlayerInfoServer.RankService/SyncRoleInfo"
	RankService_UpdateRoleRankInfo_FullMethodName  = "/Aurora.PlayerInfoServer.RankService/UpdateRoleRankInfo"
	RankService_RemoveRole_FullMethodName          = "/Aurora.PlayerInfoServer.RankService/RemoveRole"
	RankService_SyncGuildInfo_FullMethodName       = "/Aurora.PlayerInfoServer.RankService/SyncGuildInfo"
	RankService_UpdateGuildRankInfo_FullMethodName = "/Aurora.PlayerInfoServer.RankService/UpdateGuildRankInfo"
	RankService_RemoveGuild_FullMethodName         = "/Aurora.PlayerInfoServer.RankService/RemoveGuild"
	RankService_SyncPetInfo_FullMethodName         = "/Aurora.PlayerInfoServer.RankService/SyncPetInfo"
	RankService_UpdatePetRankInfo_FullMethodName   = "/Aurora.PlayerInfoServer.RankService/UpdatePetRankInfo"
	RankService_RemovePet_FullMethodName           = "/Aurora.PlayerInfoServer.RankService/RemovePet"
	RankService_RemoveTwinsTreeData_FullMethodName = "/Aurora.PlayerInfoServer.RankService/RemoveTwinsTreeData"
	RankService_AddGuildMember_FullMethodName      = "/Aurora.PlayerInfoServer.RankService/AddGuildMember"
	RankService_RemoveGuildMember_FullMethodName   = "/Aurora.PlayerInfoServer.RankService/RemoveGuildMember"
	RankService_GetGuildMember_FullMethodName      = "/Aurora.PlayerInfoServer.RankService/GetGuildMember"
)

// RankServiceClient is the client API for RankService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ServiceStart
type RankServiceClient interface {
	SyncRoleInfo(ctx context.Context, in *SyncRoleInfoRequest, opts ...grpc.CallOption) (*SyncRoleInfoReply, error)
	UpdateRoleRankInfo(ctx context.Context, in *UpdateRoleRankRequest, opts ...grpc.CallOption) (*UpdateRoleRankReply, error)
	RemoveRole(ctx context.Context, in *RemoveRoleRequest, opts ...grpc.CallOption) (*RemoveRoleReply, error)
	SyncGuildInfo(ctx context.Context, in *SyncGuildInfoRequest, opts ...grpc.CallOption) (*SyncGuildInfoReply, error)
	UpdateGuildRankInfo(ctx context.Context, in *UpdateGuildRankRequest, opts ...grpc.CallOption) (*UpdateGuildRankReply, error)
	RemoveGuild(ctx context.Context, in *RemoveGuildRequest, opts ...grpc.CallOption) (*RemoveGuildReply, error)
	SyncPetInfo(ctx context.Context, in *SyncPetInfoRequest, opts ...grpc.CallOption) (*SyncPetInfoReply, error)
	UpdatePetRankInfo(ctx context.Context, in *UpdatePetRankRequest, opts ...grpc.CallOption) (*UpdatePetRankReply, error)
	RemovePet(ctx context.Context, in *RemovePetRequest, opts ...grpc.CallOption) (*RemovePetReply, error)
	RemoveTwinsTreeData(ctx context.Context, in *RemoveTwinsDataRequest, opts ...grpc.CallOption) (*RemoveTwinsDataReply, error)
	AddGuildMember(ctx context.Context, in *GuildAddMemberRequest, opts ...grpc.CallOption) (*GuildMemberReply, error)
	RemoveGuildMember(ctx context.Context, in *GuildRemoveMemberRequest, opts ...grpc.CallOption) (*GuildMemberReply, error)
	GetGuildMember(ctx context.Context, in *GuildGetAllMemberRequest, opts ...grpc.CallOption) (*GuildGetAllMemberReply, error)
}

type rankServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRankServiceClient(cc grpc.ClientConnInterface) RankServiceClient {
	return &rankServiceClient{cc}
}

func (c *rankServiceClient) SyncRoleInfo(ctx context.Context, in *SyncRoleInfoRequest, opts ...grpc.CallOption) (*SyncRoleInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncRoleInfoReply)
	err := c.cc.Invoke(ctx, RankService_SyncRoleInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) UpdateRoleRankInfo(ctx context.Context, in *UpdateRoleRankRequest, opts ...grpc.CallOption) (*UpdateRoleRankReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRoleRankReply)
	err := c.cc.Invoke(ctx, RankService_UpdateRoleRankInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) RemoveRole(ctx context.Context, in *RemoveRoleRequest, opts ...grpc.CallOption) (*RemoveRoleReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveRoleReply)
	err := c.cc.Invoke(ctx, RankService_RemoveRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) SyncGuildInfo(ctx context.Context, in *SyncGuildInfoRequest, opts ...grpc.CallOption) (*SyncGuildInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncGuildInfoReply)
	err := c.cc.Invoke(ctx, RankService_SyncGuildInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) UpdateGuildRankInfo(ctx context.Context, in *UpdateGuildRankRequest, opts ...grpc.CallOption) (*UpdateGuildRankReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateGuildRankReply)
	err := c.cc.Invoke(ctx, RankService_UpdateGuildRankInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) RemoveGuild(ctx context.Context, in *RemoveGuildRequest, opts ...grpc.CallOption) (*RemoveGuildReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveGuildReply)
	err := c.cc.Invoke(ctx, RankService_RemoveGuild_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) SyncPetInfo(ctx context.Context, in *SyncPetInfoRequest, opts ...grpc.CallOption) (*SyncPetInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncPetInfoReply)
	err := c.cc.Invoke(ctx, RankService_SyncPetInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) UpdatePetRankInfo(ctx context.Context, in *UpdatePetRankRequest, opts ...grpc.CallOption) (*UpdatePetRankReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdatePetRankReply)
	err := c.cc.Invoke(ctx, RankService_UpdatePetRankInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) RemovePet(ctx context.Context, in *RemovePetRequest, opts ...grpc.CallOption) (*RemovePetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemovePetReply)
	err := c.cc.Invoke(ctx, RankService_RemovePet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) RemoveTwinsTreeData(ctx context.Context, in *RemoveTwinsDataRequest, opts ...grpc.CallOption) (*RemoveTwinsDataReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveTwinsDataReply)
	err := c.cc.Invoke(ctx, RankService_RemoveTwinsTreeData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) AddGuildMember(ctx context.Context, in *GuildAddMemberRequest, opts ...grpc.CallOption) (*GuildMemberReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GuildMemberReply)
	err := c.cc.Invoke(ctx, RankService_AddGuildMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) RemoveGuildMember(ctx context.Context, in *GuildRemoveMemberRequest, opts ...grpc.CallOption) (*GuildMemberReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GuildMemberReply)
	err := c.cc.Invoke(ctx, RankService_RemoveGuildMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) GetGuildMember(ctx context.Context, in *GuildGetAllMemberRequest, opts ...grpc.CallOption) (*GuildGetAllMemberReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GuildGetAllMemberReply)
	err := c.cc.Invoke(ctx, RankService_GetGuildMember_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RankServiceServer is the server API for RankService service.
// All implementations must embed UnimplementedRankServiceServer
// for forward compatibility.
//
// ServiceStart
type RankServiceServer interface {
	SyncRoleInfo(context.Context, *SyncRoleInfoRequest) (*SyncRoleInfoReply, error)
	UpdateRoleRankInfo(context.Context, *UpdateRoleRankRequest) (*UpdateRoleRankReply, error)
	RemoveRole(context.Context, *RemoveRoleRequest) (*RemoveRoleReply, error)
	SyncGuildInfo(context.Context, *SyncGuildInfoRequest) (*SyncGuildInfoReply, error)
	UpdateGuildRankInfo(context.Context, *UpdateGuildRankRequest) (*UpdateGuildRankReply, error)
	RemoveGuild(context.Context, *RemoveGuildRequest) (*RemoveGuildReply, error)
	SyncPetInfo(context.Context, *SyncPetInfoRequest) (*SyncPetInfoReply, error)
	UpdatePetRankInfo(context.Context, *UpdatePetRankRequest) (*UpdatePetRankReply, error)
	RemovePet(context.Context, *RemovePetRequest) (*RemovePetReply, error)
	RemoveTwinsTreeData(context.Context, *RemoveTwinsDataRequest) (*RemoveTwinsDataReply, error)
	AddGuildMember(context.Context, *GuildAddMemberRequest) (*GuildMemberReply, error)
	RemoveGuildMember(context.Context, *GuildRemoveMemberRequest) (*GuildMemberReply, error)
	GetGuildMember(context.Context, *GuildGetAllMemberRequest) (*GuildGetAllMemberReply, error)
	mustEmbedUnimplementedRankServiceServer()
}

// UnimplementedRankServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRankServiceServer struct{}

func (UnimplementedRankServiceServer) SyncRoleInfo(context.Context, *SyncRoleInfoRequest) (*SyncRoleInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncRoleInfo not implemented")
}
func (UnimplementedRankServiceServer) UpdateRoleRankInfo(context.Context, *UpdateRoleRankRequest) (*UpdateRoleRankReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRoleRankInfo not implemented")
}
func (UnimplementedRankServiceServer) RemoveRole(context.Context, *RemoveRoleRequest) (*RemoveRoleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveRole not implemented")
}
func (UnimplementedRankServiceServer) SyncGuildInfo(context.Context, *SyncGuildInfoRequest) (*SyncGuildInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncGuildInfo not implemented")
}
func (UnimplementedRankServiceServer) UpdateGuildRankInfo(context.Context, *UpdateGuildRankRequest) (*UpdateGuildRankReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGuildRankInfo not implemented")
}
func (UnimplementedRankServiceServer) RemoveGuild(context.Context, *RemoveGuildRequest) (*RemoveGuildReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveGuild not implemented")
}
func (UnimplementedRankServiceServer) SyncPetInfo(context.Context, *SyncPetInfoRequest) (*SyncPetInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncPetInfo not implemented")
}
func (UnimplementedRankServiceServer) UpdatePetRankInfo(context.Context, *UpdatePetRankRequest) (*UpdatePetRankReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetRankInfo not implemented")
}
func (UnimplementedRankServiceServer) RemovePet(context.Context, *RemovePetRequest) (*RemovePetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemovePet not implemented")
}
func (UnimplementedRankServiceServer) RemoveTwinsTreeData(context.Context, *RemoveTwinsDataRequest) (*RemoveTwinsDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveTwinsTreeData not implemented")
}
func (UnimplementedRankServiceServer) AddGuildMember(context.Context, *GuildAddMemberRequest) (*GuildMemberReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGuildMember not implemented")
}
func (UnimplementedRankServiceServer) RemoveGuildMember(context.Context, *GuildRemoveMemberRequest) (*GuildMemberReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveGuildMember not implemented")
}
func (UnimplementedRankServiceServer) GetGuildMember(context.Context, *GuildGetAllMemberRequest) (*GuildGetAllMemberReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGuildMember not implemented")
}
func (UnimplementedRankServiceServer) mustEmbedUnimplementedRankServiceServer() {}
func (UnimplementedRankServiceServer) testEmbeddedByValue()                     {}

// UnsafeRankServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RankServiceServer will
// result in compilation errors.
type UnsafeRankServiceServer interface {
	mustEmbedUnimplementedRankServiceServer()
}

func RegisterRankServiceServer(s grpc.ServiceRegistrar, srv RankServiceServer) {
	// If the following call pancis, it indicates UnimplementedRankServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RankService_ServiceDesc, srv)
}

func _RankService_SyncRoleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncRoleInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).SyncRoleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_SyncRoleInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).SyncRoleInfo(ctx, req.(*SyncRoleInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_UpdateRoleRankInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRoleRankRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).UpdateRoleRankInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_UpdateRoleRankInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).UpdateRoleRankInfo(ctx, req.(*UpdateRoleRankRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_RemoveRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).RemoveRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_RemoveRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).RemoveRole(ctx, req.(*RemoveRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_SyncGuildInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncGuildInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).SyncGuildInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_SyncGuildInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).SyncGuildInfo(ctx, req.(*SyncGuildInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_UpdateGuildRankInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildRankRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).UpdateGuildRankInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_UpdateGuildRankInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).UpdateGuildRankInfo(ctx, req.(*UpdateGuildRankRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_RemoveGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveGuildRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).RemoveGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_RemoveGuild_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).RemoveGuild(ctx, req.(*RemoveGuildRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_SyncPetInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncPetInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).SyncPetInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_SyncPetInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).SyncPetInfo(ctx, req.(*SyncPetInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_UpdatePetRankInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetRankRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).UpdatePetRankInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_UpdatePetRankInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).UpdatePetRankInfo(ctx, req.(*UpdatePetRankRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_RemovePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemovePetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).RemovePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_RemovePet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).RemovePet(ctx, req.(*RemovePetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_RemoveTwinsTreeData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveTwinsDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).RemoveTwinsTreeData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_RemoveTwinsTreeData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).RemoveTwinsTreeData(ctx, req.(*RemoveTwinsDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_AddGuildMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildAddMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).AddGuildMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_AddGuildMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).AddGuildMember(ctx, req.(*GuildAddMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_RemoveGuildMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildRemoveMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).RemoveGuildMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_RemoveGuildMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).RemoveGuildMember(ctx, req.(*GuildRemoveMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_GetGuildMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildGetAllMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).GetGuildMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_GetGuildMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).GetGuildMember(ctx, req.(*GuildGetAllMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RankService_ServiceDesc is the grpc.ServiceDesc for RankService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RankService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Aurora.PlayerInfoServer.RankService",
	HandlerType: (*RankServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncRoleInfo",
			Handler:    _RankService_SyncRoleInfo_Handler,
		},
		{
			MethodName: "UpdateRoleRankInfo",
			Handler:    _RankService_UpdateRoleRankInfo_Handler,
		},
		{
			MethodName: "RemoveRole",
			Handler:    _RankService_RemoveRole_Handler,
		},
		{
			MethodName: "SyncGuildInfo",
			Handler:    _RankService_SyncGuildInfo_Handler,
		},
		{
			MethodName: "UpdateGuildRankInfo",
			Handler:    _RankService_UpdateGuildRankInfo_Handler,
		},
		{
			MethodName: "RemoveGuild",
			Handler:    _RankService_RemoveGuild_Handler,
		},
		{
			MethodName: "SyncPetInfo",
			Handler:    _RankService_SyncPetInfo_Handler,
		},
		{
			MethodName: "UpdatePetRankInfo",
			Handler:    _RankService_UpdatePetRankInfo_Handler,
		},
		{
			MethodName: "RemovePet",
			Handler:    _RankService_RemovePet_Handler,
		},
		{
			MethodName: "RemoveTwinsTreeData",
			Handler:    _RankService_RemoveTwinsTreeData_Handler,
		},
		{
			MethodName: "AddGuildMember",
			Handler:    _RankService_AddGuildMember_Handler,
		},
		{
			MethodName: "RemoveGuildMember",
			Handler:    _RankService_RemoveGuildMember_Handler,
		},
		{
			MethodName: "GetGuildMember",
			Handler:    _RankService_GetGuildMember_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "microservices/rank/v1/rank.proto",
}

const (
	RankListHttp_GetRankListData_FullMethodName     = "/Aurora.PlayerInfoServer.RankListHttp/GetRankListData"
	RankListHttp_GetTwinTreeRankList_FullMethodName = "/Aurora.PlayerInfoServer.RankListHttp/GetTwinTreeRankList"
	RankListHttp_GetPetInfo_FullMethodName          = "/Aurora.PlayerInfoServer.RankListHttp/GetPetInfo"
)

// RankListHttpClient is the client API for RankListHttp service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RankListHttpClient interface {
	GetRankListData(ctx context.Context, in *GetRankListReq, opts ...grpc.CallOption) (*GetRankListReply, error)
	GetTwinTreeRankList(ctx context.Context, in *GetTwinsTreeRankListReq, opts ...grpc.CallOption) (*GetTwinsTreeRankListReply, error)
	GetPetInfo(ctx context.Context, in *GetPetInfoReq, opts ...grpc.CallOption) (*GetPetInfoReply, error)
}

type rankListHttpClient struct {
	cc grpc.ClientConnInterface
}

func NewRankListHttpClient(cc grpc.ClientConnInterface) RankListHttpClient {
	return &rankListHttpClient{cc}
}

func (c *rankListHttpClient) GetRankListData(ctx context.Context, in *GetRankListReq, opts ...grpc.CallOption) (*GetRankListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRankListReply)
	err := c.cc.Invoke(ctx, RankListHttp_GetRankListData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankListHttpClient) GetTwinTreeRankList(ctx context.Context, in *GetTwinsTreeRankListReq, opts ...grpc.CallOption) (*GetTwinsTreeRankListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTwinsTreeRankListReply)
	err := c.cc.Invoke(ctx, RankListHttp_GetTwinTreeRankList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankListHttpClient) GetPetInfo(ctx context.Context, in *GetPetInfoReq, opts ...grpc.CallOption) (*GetPetInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPetInfoReply)
	err := c.cc.Invoke(ctx, RankListHttp_GetPetInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RankListHttpServer is the server API for RankListHttp service.
// All implementations must embed UnimplementedRankListHttpServer
// for forward compatibility.
type RankListHttpServer interface {
	GetRankListData(context.Context, *GetRankListReq) (*GetRankListReply, error)
	GetTwinTreeRankList(context.Context, *GetTwinsTreeRankListReq) (*GetTwinsTreeRankListReply, error)
	GetPetInfo(context.Context, *GetPetInfoReq) (*GetPetInfoReply, error)
	mustEmbedUnimplementedRankListHttpServer()
}

// UnimplementedRankListHttpServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRankListHttpServer struct{}

func (UnimplementedRankListHttpServer) GetRankListData(context.Context, *GetRankListReq) (*GetRankListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRankListData not implemented")
}
func (UnimplementedRankListHttpServer) GetTwinTreeRankList(context.Context, *GetTwinsTreeRankListReq) (*GetTwinsTreeRankListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTwinTreeRankList not implemented")
}
func (UnimplementedRankListHttpServer) GetPetInfo(context.Context, *GetPetInfoReq) (*GetPetInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetInfo not implemented")
}
func (UnimplementedRankListHttpServer) mustEmbedUnimplementedRankListHttpServer() {}
func (UnimplementedRankListHttpServer) testEmbeddedByValue()                      {}

// UnsafeRankListHttpServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RankListHttpServer will
// result in compilation errors.
type UnsafeRankListHttpServer interface {
	mustEmbedUnimplementedRankListHttpServer()
}

func RegisterRankListHttpServer(s grpc.ServiceRegistrar, srv RankListHttpServer) {
	// If the following call pancis, it indicates UnimplementedRankListHttpServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RankListHttp_ServiceDesc, srv)
}

func _RankListHttp_GetRankListData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankListHttpServer).GetRankListData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankListHttp_GetRankListData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankListHttpServer).GetRankListData(ctx, req.(*GetRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankListHttp_GetTwinTreeRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTwinsTreeRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankListHttpServer).GetTwinTreeRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankListHttp_GetTwinTreeRankList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankListHttpServer).GetTwinTreeRankList(ctx, req.(*GetTwinsTreeRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankListHttp_GetPetInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankListHttpServer).GetPetInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankListHttp_GetPetInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankListHttpServer).GetPetInfo(ctx, req.(*GetPetInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RankListHttp_ServiceDesc is the grpc.ServiceDesc for RankListHttp service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RankListHttp_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Aurora.PlayerInfoServer.RankListHttp",
	HandlerType: (*RankListHttpServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRankListData",
			Handler:    _RankListHttp_GetRankListData_Handler,
		},
		{
			MethodName: "GetTwinTreeRankList",
			Handler:    _RankListHttp_GetTwinTreeRankList_Handler,
		},
		{
			MethodName: "GetPetInfo",
			Handler:    _RankListHttp_GetPetInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "microservices/rank/v1/rank.proto",
}
