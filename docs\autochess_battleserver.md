# 🎮 4人自走棋BattleServer技术文档

## 📋 目录

- [1. 系统架构概述](#1-系统架构概述)
- [2. 协议交互流程](#2-协议交互流程)
- [3. 英雄属性系统](#3-英雄属性系统)
- [4. 核心组件设计](#4-核心组件设计)
- [5. 时间配置与超时机制](#5-时间配置与超时机制)
- [6. 机器人托管系统](#6-机器人托管系统)
- [7. 对手匹配机制](#7-对手匹配机制)
- [8. 资源管理与清理](#8-资源管理与清理)
- [9. 玩家登出清理机制](#9-玩家登出清理机制)
- [10. 战斗流程控制](#10-战斗流程控制)
- [11. 性能指标](#11-性能指标)
- [12. 事件驱动架构](#12-事件驱动架构)
- [13. 总结](#13-总结)

---

## 1. 系统架构概述

### 1.1 整体架构设计

BattleServer采用**组件化、事件驱动**的架构设计，支持4人自走棋的完整战斗流程：

```text
Program.cs (启动入口)
├── BattleService.cs (RPC接口层)
├── AutoChessScene (4人自走棋场景)
│   ├── BattleStateManager (状态机管理)
│   ├── PlayerManager (玩家数据管理)
│   ├── BattleInstanceManager (战斗实例管理)
│   ├── OpponentPairManager (对手配对管理)
│   ├── BuffManager (Buff管理)
│   ├── CheckerBoard (棋盘管理)
│   └── BattleEventBus (事件总线)
├── SceneManager (场景生命周期管理)
└── NatsClient/NatsServer (跨服通信)
```

### 1.2 核心特性

- **真正的4人自走棋**：支持同时进行的2场1v1战斗
- **独立棋盘管理**：每场战斗有独立的CheckerBoard和临时位
- **正确的操作路由**：玩家操作路由到对应的战斗实例
- **同步等待机制**：确保所有战斗完成后才进入结算
- **配置驱动**：所有参数从PlayMode表读取
- **事件驱动架构**：使用BattleEventBus实现组件间解耦
- **坐标系统转换**：支持客户端与服务器坐标的双向转换
- **依赖注入模式**：组件间通过依赖注入实现松耦合
- **状态机驱动**：完整的8状态战斗流程控制

---

## 2. 协议交互流程

### 2.1 协议映射关系

#### 2.1.1 客户端 ↔ GameServer (CL/LC协议)

| 客户端请求 | GameServer响应 | 功能 | 对应RPC |
|-----------|---------------|------|---------|
| `CLMatchReq` | `LCMatchRsp` | 匹配请求 | → MatchServer |
| `CLRoundBattleStartReq` | `LCRoundBattleStartResp` | 回合开始/确认结算 | → `EnterBattleReq` |
| `CLReadyReq` | `LCReadyRsp` | 准备完成 | → `ReadyBattleReq` |
| `CLRoundBattleEndReq` | `LCRoundBattleEndResp` | 战斗结束 | → `EndBattleReq` |
| `CLSelectBufferReq` | `LCSelectBufferResp` | 选择Buff | → `SelectBufferReq` |
| `CLMergeReq` | `LCMergeRsp` | 英雄移动与合成 | → `MergeHeroReq` |

#### 2.1.2 英雄合成协议详细说明

**协议字段变更**：

- **客户端协议** (`LCMergeRsp`): 返回完整的棋盘格子信息
  - `NewHero` (PBCheckerBoard): 包含格子ID和英雄详细信息
  - 格子信息: `GridID` (int32) - 合成后英雄所在格子
  - 英雄信息: `Hero` (PBBattleHeroInfo) - 包含ID、等级、星级、觉醒等级

- **服务端协议** (`MergeHeroResp`): 返回英雄战斗信息
  - `NewHero` (PBBattleHeroInfo): 英雄的完整战斗属性
  - 包含字段: `Id`, `Level`, `StarLevel`, `AwakeLevel`

**英雄属性说明**：

- **英雄等级** (`Level`): **局外养成属性**，玩家在战斗外通过经验值提升
- **英雄星级** (`StarLevel`): **局内合成属性**，战斗中通过合成相同星级的相同英雄提升（1星→2星→3星）
- **觉醒等级** (`AwakeLevel`): **局外养成属性**，玩家在战斗外通过觉醒材料提升，**默认为0级**

**类型转换处理**：

```go
// GameServer中的类型转换逻辑
if resp.NewHero != nil {
    ret.NewHero = &public.PBCheckerBoard{
        GridID: resp.To, // 使用目标格子ID
        Hero:   resp.NewHero, // BattleServer返回的英雄信息
    }
}
```

#### 2.1.4 GameServer ↔ BattleServer (RPC协议)

| GameServer请求 | BattleServer响应 | 功能 | 触发时机 |
|---------------|-----------------|------|----------|
| `CreateBattleReq` | `CreateBattleResp` | 创建战斗 | 匹配成功后 |
| `EnterBattleReq` | `EnterBattleResp` | 进入战斗 | 客户端确认回合开始 |
| `ReadyBattleReq` | `ReadyBattleResp` | 准备完成 | 客户端准备完成 |
| `EndBattleReq` | `EndBattleResp` | 战斗结束 | 客户端确认战斗结束 |
| `SelectBufferReq` | `SelectBufferResp` | 选择Buff | 客户端选择Buff |
| `MergeHeroReq` | `MergeHeroResp` | 英雄移动与合成 | 客户端移动/合成英雄 |
| `PlayerLogoutReq` | `PlayerLogoutResp` | 玩家登出 | 玩家断线/退出 |
| `BattleStateChangeReq` | `BattleStateChangeResp` | 战斗状态变化（推送） | BattleServer主动推送 |

#### 2.1.5 GameServer ← BattleServer (主动推送)

| BattleServer推送       | GameServer接收           | 转发给客户端               | 触发时机     | 关键字段 |
| ---------------------- | ------------------------ | -------------------------- | ------------ | -------- |
| `RoundStartReq`        | `RoundStart()`           | `LCRoundStartNotify`       | 回合开始     | - |
| `RoundBattleStartReq`  | `RoundBattleStart()`     | `LCRoundBattleStartNotify` | 战斗开始     | - |
| `RoundBattleEndReq`    | `RoundBattleEnd()`       | `LCRoundBattleEndNotify`   | 单场战斗结束 | `isEnd`: 是否为最后回合 |
| `BattleEndReq`         | `BattleEnd()`            | `LCBattleEndNotify`        | 整场游戏结束 | - |
| `BattleStateChangeReq` | `OnBattleStateChanged()` | -                          | 状态变化     | - |

### 2.2 协议字段详细说明

#### 2.2.1 `RoundBattleEndReq` / `LCRoundBattleEndNotify`

```protobuf
message RoundBattleEndReq {
  uint64 uid = 1;     // 玩家ID
  uint64 winUid = 2;  // 获胜者ID
  uint64 loseUid = 3; // 失败者ID
  bool isEnd = 4;     // 是否为最后回合（整场比赛即将结束）
}

message LCRoundBattleEndNotify {
  uint64 winUid = 1;  // 获胜者ID
  uint64 loseUid = 2; // 失败者ID
  bool isEnd = 3;     // 是否为最后回合（整场比赛即将结束）
}
```

**最后回合判断逻辑**：

- 当剩余活跃玩家数量 ≤ 1 时，`isEnd = true`
- 客户端收到 `isEnd = true` 时，应准备整场比赛结算界面

### 2.3 GameServer ↔ BattleServer (RPC调用)

| GameServer调用 | BattleServer处理 | 功能 |
|---------------|-----------------|------|
| `CreateBattleReq` | `CreateBattle()` | 创建战斗 |
| `EnterBattleReq` | `EnterBattle()` | 进入战斗/确认结算 |
| `ReadyBattleReq` | `UserRead()` | 准备完成 |
| `EndBattleReq` | `EndBattle()` | 战斗结束 |
| `SelectBufferReq` | `SelectBuffer()` | 选择Buff |
| `MergeHeroReq` | `MergeHero()` | 英雄移动与合成 |

### 2.4 完整对战流程

#### 2.4.1 阶段1：匹配和创建战斗

1. 客户端 → GameServer: `CLMatchReq`
2. GameServer → MatchServer: `MatchRequest`
3. MatchServer匹配4名玩家 → BattleServer: `CreateBattleReq`
4. BattleServer创建AutoChessScene，调用`InitBattleWithoutStart()`，状态: StateNone
5. MatchServer → GameServer: `MatchResultRequest` (通知匹配成功，**不含血量**)
6. **GameServer计算血量**：读取PlayMode表，根据最高奖杯数计算统一血量
7. GameServer → 客户端: `LCMatchSuccessNotify` (**包含正确血量**)

#### 2.4.2 阶段2：第一回合开始

8. 客户端 → GameServer: `CLRoundBattleStartReq` (4名玩家)
9. GameServer → BattleServer: `EnterBattleReq` (4次调用)
10. BattleServer收到所有玩家进入 → 调用`StartBattleStateMachine()`
11. 状态转换: StateNone → StateRoundStart (触发`OnBattleStateChanged`事件)
12. 事件处理: 调用`HandleRoundStart()` → 对手配对 → 创建战斗实例
13. **关键修复**: 如果是Buff回合，先调用`GenerateBuffOptionsForAllPlayers()`生成Buff选项
14. BattleServer → GameServer: `RoundStartReq` (推送给4名玩家，包含个性化Buff选项)
15. GameServer → 客户端: `LCRoundStartNotify` (棋盘信息+Buff选择)

#### 2.4.3 阶段3：准备阶段

16. 状态转换: StateRoundStart → StatePreparation (触发`HandlePreparationPhase()`)
17. 如果是Buff回合：Buff选项已在StateRoundStart阶段生成，机器人自动选择Buff
17. 客户端选择Buff → GameServer: CLSelectBufferReq
18. GameServer → BattleServer: SelectBufferReq → 调用`SelectBuff()`
19. Buff选择成功后自动生成新英雄并返回给客户端
20. 客户端操作英雄 → GameServer: CLMergeReq
21. GameServer → BattleServer: MergeHeroReq → 调用`MergeHero()`（包含坐标转换）
22. 客户端准备完成 → GameServer: CLReadyReq
23. GameServer → BattleServer: ReadyBattleReq → 调用`SetPlayerReady()`
24. BattleServer检测所有玩家准备完毕 → 触发`AllPlayersReadyEvent`
25. 状态转换: StatePreparation → StateBattleStarting

#### **阶段4：战斗开始**

26. 状态转换: StateBattleStarting → StateBattleInProgress
27. 事件处理: 应用Buff效果 → 推送战斗开始通知
28. BattleServer → GameServer: RoundBattleStartReq (推送给4名玩家，包含坐标转换)
29. GameServer → 客户端: LCRoundBattleStartNotify (双方阵容信息)
30. 机器人自动处理：机器人vs机器人立即结算，机器人vs真人等待真人操作

#### **阶段5：战斗结束**

31. 客户端战斗完成 → GameServer: CLRoundBattleEndReq (胜负结果)
32. GameServer → BattleServer: EndBattleReq → 调用`HandleBattleEnd()`
33. 真人vs机器人：机器人自动发送相反结果
34. BattleServer收到同一实例2名玩家的EndBattleReq → 立即推送结果
35. BattleServer → GameServer: RoundBattleEndReq (推送给对应2名玩家)
36. GameServer → 客户端: LCRoundBattleEndNotify (胜负结果)
37. 所有战斗实例完成 → 调用`_battleStateManager.EndBattle()`
38. 状态转换: StateBattleInProgress → StateRoundSettlement → StateEliminationCheck

#### **阶段6：回合确认和下一回合**

39. 淘汰检查：血量为0的玩家被淘汰，机器人自动确认结算
40. 客户端看完结算 → GameServer: CLRoundBattleStartReq (确认结算)
41. GameServer → BattleServer: EnterBattleReq → 调用`HandleRoundConfirmation()`
42. 真人确认时，所有机器人自动确认
43. BattleServer收到所有玩家确认 → 状态转换: StateEliminationCheck → StateRoundStart
44. 重复步骤12-43，直到游戏结束

#### 2.4.7 阶段7：游戏结束

35. 只剩1名玩家 → 状态转换: StateEliminationCheck → StateGameOver
36. BattleServer → GameServer: `BattleEndReq` (推送给所有玩家)
37. GameServer → 客户端: `LCBattleEndNotify` (最终排名)
38. **延迟清理机制**：BattleServer延迟5秒后清理资源，确保GameServer有足够时间处理通知

#### 2.4.8 游戏结束处理

游戏结束时采用延迟清理机制，确保GameServer有足够时间处理通知：

```csharp
// 延迟清理实现
private void ScheduleDelayedCleanup()
{
    Log.Info($"[{_logName}] Scheduling delayed cleanup in 5 seconds");
    var cleanupTimer = new System.Threading.Timer((_) =>
    {
        BattleService.CleanupBattle(BattleId);
    }, null, TimeSpan.FromSeconds(5), Timeout.InfiniteTimeSpan);
}
```

---

## 3. 英雄属性系统

### 3.1 核心概念区分

#### 3.1.1 阵容 (Lineup) vs 棋盘单位 (Board Units)

**阵容 (Lineup)**：

- **定义**：玩家拥有的英雄池，在`CreateBattle`时传入
- **特点**：
  - 每个英雄ID唯一，不重复
  - 包含Level和AwakeLevel（局外养成属性）
  - **不包含StarLevel**（星级是棋盘概念）
  - **不包含位置信息**（位置是棋盘概念）
- **数据结构**：`List<PBBattleHeroInfo>`（无重复ID）

**棋盘单位 (Board Units)**：

- **定义**：每回合从阵容中随机生成到棋盘上的英雄实例
- **特点**：
  - 可以有相同英雄ID的多个实例
  - 继承阵容中的Level和AwakeLevel
  - **新增StarLevel**（初始1星，可通过合成提升）
  - **有位置信息**（PBCheckerBoard.GridID）
- **数据结构**：`List<PBCheckerBoard>`（可重复ID，有位置）

### 3.2 属性分类

#### 3.2.1 局外养成属性（战斗前确定，战斗中不变）

- **英雄等级** (`Level`):
  - 玩家在战斗外通过消耗经验值提升
  - 影响英雄的基础属性（攻击力、生命值等）
  - **数据来源**：从玩家阵容中读取
  - **战斗中行为**：保持不变，所有同ID英雄共享相同等级

- **觉醒等级** (`AwakeLevel`):
  - 玩家在战斗外通过消耗觉醒材料提升
  - 解锁英雄的特殊技能或大幅提升属性
  - **默认为0级**（未觉醒状态）
  - **数据来源**：从玩家阵容中读取
  - **战斗中行为**：保持不变，所有同ID英雄共享相同觉醒等级

#### 3.2.2 局内战斗属性（战斗中动态变化）

- **英雄星级** (`StarLevel`):
  - **初始值**: 所有英雄生成到棋盘时均为1星
  - **提升方式**: 战斗中通过合成相同星级的相同英雄提升
  - **合成规则**: 两个相同英雄ID且相同星级的英雄合成 → 星级+1
  - **星级上限**: 通常为3星（1星→2星→3星）
  - **属性影响**: 星级越高，英雄战斗力越强
  - **独立性**: 每个棋盘单位有独立的星级

### **数据流转机制**

#### **阶段1：CreateBattle - 阵容传入**
```
GameServer → BattleServer: 玩家阵容数据
- 数据格式: List<PBBattleHeroInfo>
- 每个英雄ID唯一（如：[剑士Lv.5觉醒0, 法师Lv.3觉醒1, 弓手Lv.7觉醒2]）
- 包含真实的Level和AwakeLevel
- 不包含StarLevel和位置信息
```

#### **阶段2：英雄生成 - 阵容到棋盘**
```
每回合开始: 从阵容随机生成棋盘单位
- 随机选择阵容中的英雄ID（可重复选择）
- 继承阵容中的Level和AwakeLevel
- StarLevel初始化为1星
- 分配棋盘位置（GridID）
- 结果: 可能生成多个相同ID的英雄实例
```

#### **阶段3：英雄合成 - 棋盘单位合成**
```csharp
// 英雄合成的核心判断逻辑
bool CanMerge(Entity source, Entity target)
{
    return source.ConfigId == target.ConfigId &&      // 相同英雄ID
           source.StarLevel == target.StarLevel &&    // 相同星级
           target.StarLevel < MaxStarLevel;           // 未达到星级上限
}
```

**合成结果**：
- **源英雄**: 被消耗，从棋盘移除
- **目标英雄**: 星级+1，保持在原位置
- **属性继承**: Level和AwakeLevel从阵容获取（保持不变），只有StarLevel提升

#### **合成示例**
```
阵容中: 剑士(Lv.5, 觉醒0)
棋盘上: 1星剑士(Lv.5, 觉醒0, 位置A) + 1星剑士(Lv.5, 觉醒0, 位置B)
合成后: 2星剑士(Lv.5, 觉醒0, 位置A) // Level和AwakeLevel来自阵容，StarLevel提升
```

### **实现架构**

#### **PlayerManager - 阵容数据管理**
```csharp
public class PlayerManager
{
    // 存储玩家完整阵容信息
    private Dictionary<long, List<PBBattleHeroInfo>> playerHeroInfos;

    // 根据英雄ID获取阵容中的英雄信息（Level和AwakeLevel）
    public PBBattleHeroInfo GetLineupHeroInfo(long playerId, int heroId)
    {
        var heroInfos = GetPlayerHeroInfos(playerId);
        return heroInfos.FirstOrDefault(h => h.Id == heroId);
    }
}
```

#### **棋盘单位创建 - 继承阵容属性**
```csharp
// 从阵容获取局外养成信息
var lineupHeroInfo = _playerManager.GetLineupHeroInfo(playerId, entity.ConfigId);

new PBBattleHeroInfo
{
    Id = entity.ConfigId,
    Level = lineupHeroInfo?.Level ?? 1,        // 阵容中的真实等级
    StarLevel = 1,                             // 新生成英雄都是1星
    AwakeLevel = lineupHeroInfo?.AwakeLevel ?? 0 // 阵容中的真实觉醒等级
}
```

#### **英雄合成 - 保持阵容属性**
```csharp
// 合成时从阵容获取Level和AwakeLevel
var lineupHeroInfo = _playerManager.GetLineupHeroInfo(playerId, mergedEntity.ConfigId);

newHero = new PBBattleHeroInfo
{
    Id = mergedEntity.ConfigId,
    Level = lineupHeroInfo?.Level ?? 1,        // 阵容中的真实等级
    StarLevel = mergedEntity.StarLevel,         // 合成后的星级（局内变化）
    AwakeLevel = lineupHeroInfo?.AwakeLevel ?? 0 // 阵容中的真实觉醒等级
};
```

#### **数据一致性保证**
- **局外养成属性**（Level、AwakeLevel）：始终从阵容获取，战斗中不变
- **局内战斗属性**（StarLevel）：棋盘单位独有，可通过合成变化
- **位置信息**：棋盘单位独有，通过PBCheckerBoard.GridID标识
- **ID重复处理**：通过位置区分相同ID的不同棋盘单位

------

## 4. 核心组件设计

### 4.1 BattleService (RPC接口层)

- **职责**：处理来自GameServer的RPC请求
- **关键特性**：
  - 线程安全的战斗管理（ConcurrentDictionary）
  - 玩家进入状态跟踪机制
  - 超时处理和机器人托管
  - 战斗生命周期管理

### 4.2 AutoChessScene (4人自走棋核心)

- **职责**：管理完整的4人自走棋战斗流程
- **关键特性**：
  - **事件驱动架构**：使用BattleEventBus实现组件间通信
  - **依赖注入模式**：构造函数注入各个管理器组件
  - **状态机驱动**：通过BattleStateManager控制8个核心状态
  - **坐标系统转换**：支持客户端与服务器坐标的双向转换
  - **战斗实例管理**：通过BattleInstanceManager统一管理2个并行1v1战斗
  - **玩家数据管理**：通过PlayerManager管理阵容、血量、状态等
  - **Buff系统集成**：完整的Buff选择、应用和管理流程
  - **机器人托管**：自动处理机器人的所有操作（准备、选择、战斗）

### 4.3 BattleStateManager (状态机)

- **职责**：管理8个核心战斗状态的转换
- **状态流程**：
  ```text
  StateNone → StateRoundStart → StatePreparation →
  StateBattleStarting → StateBattleInProgress →
  StateRoundSettlement → StateEliminationCheck → StateGameOver
  ```
- **关键特性**：
  - **事件驱动**：状态变化通过BattleEventBus发布事件
  - **超时机制**：每个状态都有对应的超时处理
  - **回合计数**：跟踪当前回合数和Buff选择回合

### 4.4 BattleInstanceManager (战斗实例管理)

- **职责**：管理4人自走棋中的2个并行1v1战斗
- **关键特性**：
  - **动态实例创建**：根据对手配对创建战斗实例
  - **独立棋盘管理**：每个实例有独立的CheckerBoard
  - **玩家映射管理**：维护玩家ID到战斗实例的映射关系
  - **英雄合成处理**：支持移动操作和合成操作的坐标转换
  - **战斗完成同步**：等待所有实例完成后进入结算
- **依赖关系**：
  - **依赖PlayerManager**：获取玩家阵容中的英雄属性（Level、AwakeLevel）
  - **坐标转换支持**：处理客户端GridID与服务器坐标的转换

#### **坐标系统设计**

**新架构设计**：统一绝对GridID系统
- **第一个玩家**：固定使用GridID 1-30（第1-5行）
- **第二个玩家**：固定使用GridID 31-60（第6-10行）
- **服务器处理**：统一使用绝对GridID（1-60），无需坐标转换
- **数据推送**：按照[第一个玩家, 第二个玩家]的固定顺序返回
- **客户端处理**：根据数组索引和自己的位置进行视角转换

**核心原则**：
1. **服务器简化**：BattleServer只处理绝对GridID，移除所有坐标转换逻辑
2. **数据一致性**：所有推送数据按照战斗实例中玩家顺序排列
3. **客户端负责**：客户端根据自己在数组中的位置进行视角转换

**GridID分配规则**：
```
第一个玩家区域：GridID 1-30  (第1-5行，每行6个格子)
第二个玩家区域：GridID 31-60 (第6-10行，每行6个格子)

格子编号规则：从下往上，从左往右递增
行1: 1,  2,  3,  4,  5,  6
行2: 7,  8,  9,  10, 11, 12
...
行5: 25, 26, 27, 28, 29, 30
行6: 31, 32, 33, 34, 35, 36
...
行10: 55, 56, 57, 58, 59, 60
```

#### **数据推送规则**

**统一的数据顺序**：
- **RoundStart和RoundBattleStart**：都按照战斗实例中PlayerIds的固定顺序推送
- **数组结构**：`[第一个玩家数据, 第二个玩家数据]`
- **GridID分配**：第一个玩家使用1-30，第二个玩家使用31-60

**LCRoundStartNotify（回合开始推送）**：
```json
{
  "PlayerBoards": [
    {"Uid": 第一个玩家ID, "BoardInfo": [GridID 1-30的英雄数据]},
    {"Uid": 第二个玩家ID, "BoardInfo": [GridID 31-60的英雄数据]}
  ]
}
```

**LCRoundBattleStartNotify（战斗开始推送）**：
```json
{
  "Team": [
    {"Player": 第一个玩家信息, "BoardInfo": [GridID 1-30的英雄数据]},
    {"Player": 第二个玩家信息, "BoardInfo": [GridID 31-60的英雄数据]}
  ]
}
```

**客户端处理逻辑**：
- 客户端根据自己的UID在数组中的位置确定视角
- 如果自己是数组[0]，则[0]是自己，[1]是对手
- 如果自己是数组[1]，则[1]是自己，[0]是对手

### **5. PlayerManager (玩家管理)**

- **职责**：管理4名玩家的数据和状态
- **核心功能**：
  - **阵容数据管理**：存储玩家完整英雄阵容信息（Level、AwakeLevel）
  - **英雄信息查询**：提供`GetLineupHeroInfo()`方法查询阵容中英雄属性
  - **玩家状态管理**：血量管理、准备状态、对手关系、淘汰状态
  - **棋盘数据管理**：管理玩家当前回合和上一回合的棋盘数据
  - **服务器ID管理**：存储玩家所在的GameServer ID用于消息路由
  - **基本信息管理**：玩家名称、等级、奖杯数等基本信息

#### **棋盘数据管理机制**

**PlayerBoardEntity数据结构**：
```csharp
public class PlayerBoardEntity
{
    public int ConfigId { get; set; }    // 英雄配置ID
    public int StarLevel { get; set; }   // 星级（合成进度）
    public int GridX { get; set; }       // 服务器行坐标
    public int GridY { get; set; }       // 服务器列坐标
}
```

**数据管理方法**：
- `SavePlayerBoardData()`: 保存当前回合棋盘数据
- `SavePlayerPreviousRoundBoardData()`: 保存上一回合结束时的棋盘数据
- `GetPlayerBoardData()`: 获取当前回合棋盘数据
- `GetPlayerPreviousRoundBoardData()`: 获取上一回合棋盘数据
- `GetLineupHeroInfo()`: 根据英雄ID获取阵容中的英雄信息
- `GetPlayerServerId()`: 获取玩家所在的GameServer ID

**关键特性**：
- **对手变动兼容**：棋盘数据跟随玩家，不受对手变动影响
- **回合间保持**：确保玩家的棋子在回合间正确保持位置和星级
- **历史数据支持**：支持对手查看上一回合的棋盘数据
- **跨服支持**：存储玩家服务器ID，支持跨服消息路由

### **6. CheckerBoard (棋盘管理)**

- **职责**：管理单个战斗实例的棋盘状态
- **核心功能**：
  - **实体生命周期管理**：创建、放置、移动、合成、移除实体
  - **格子状态管理**：10x6格子的占用状态和实体引用
  - **临时位管理**：支持英雄的临时存放位置
  - **坐标验证**：验证位置的有效性和边界检查
  - **实体查询**：根据玩家ID、位置等条件查询实体
- **关键方法**：
  - `CreateAndPlaceEntity()`: 创建并放置实体到指定位置
  - `MoveEntity()`: 移动实体到新位置
  - `TryMergeEntities()`: 尝试合成两个实体
  - `GetPlayerEntities()`: 获取指定玩家的所有实体
  - `CleanupOrphanedEntities()`: 清理孤立的实体引用

### **7. BuffManager (Buff管理)**

- **职责**：管理战斗中的Buff系统
- **核心功能**：
  - **Buff选项生成**：为玩家生成个性化的Buff选择
  - **Buff选择处理**：处理玩家的Buff选择操作
  - **Buff效果应用**：在战斗开始时应用选中的Buff
  - **随机选择支持**：超时时自动随机选择Buff

### **8. OpponentPairManager (对手配对)**

- **职责**：实现策划文档的对手配对规则
- **功能**：随机配对、血量排序、避重机制、淘汰处理

#### **淘汰机制处理**

根据策划文档要求，实现不同淘汰人数下的配对策略：

**0人淘汰**：正常4人配对
- 第1回合：随机配对
- 后续回合：按血量排序配对，避免连续对战同一对手

**1人淘汰**：保持4人匹配队列，被淘汰者填充匹配位置
```csharp
// 活跃玩家：[A(3血), B(2血), C(1血)]
// 淘汰玩家：[D(0血)]
// 配对结果：A vs B (正常对战), C vs D (C自动获胜)
```

**2人淘汰**：移出匹配队列，剩余2人对战
```csharp
// 活跃玩家：[A(2血), B(1血)]
// 配对结果：A vs B (最终决战)
```

**3人淘汰**：游戏结束
```csharp
// 活跃玩家：[A(1血)]
// 结果：A获胜，游戏结束
```

#### **实例管理优化**

`BattleInstanceManager`针对淘汰玩家的特殊处理：
- 检测与淘汰玩家的配对
- 活跃玩家自动获胜
- 正确处理只有一个玩家的实例

---

## 5. 时间配置与超时机制

### **配置来源**

从`PlayMode.json`配置文件读取：
```json
{
  "PreDuration": 60,    // 准备阶段时长(秒)
  "BattleDuration": 60,   // 战斗阶段时长(秒)
  "BuffDuration": [20],   // Buff选择时长(秒)
  "SpeedUpTime": 10,    // 战斗加速开始时间(秒)
  "BuffRound": [2,4,6],   // 提供Buff的回合数
  "PlayerHP": [[0,999,3],[1000,100000,4]] // 奖杯段位对应血量
}
```

### **各阶段时间配置**

| 阶段                      | 配置时间 | 实际时间（+5s缓冲） | 超时处理           |
| ------------------------- | -------- | ------------------- | ------------------ |
| **StatePreparation**      | 60秒     | 65秒                | 强制未准备玩家准备 |
| **StateBattleInProgress** | 60秒     | 65秒                | 强制结束未完成战斗 |
| **StateBattleStarting**   | 1秒      | 1秒                 | 战斗开始提示       |
| **StateRoundSettlement**  | 2秒      | 2秒                 | 回合结算展示       |
| **StateEliminationCheck** | 1秒      | 1秒                 | 强制未确认玩家确认 |
| **Buff选择时间**          | 20秒     | 25秒                | 强制随机选择buff   |
| **战斗加速时间**          | 10秒后   | 10秒后              | 客户端3倍速播放    |

### **5秒缓冲时间机制**

为了同步服务器与客户端的动画时间，所有主要阶段都添加了5秒缓冲时间：

- **PreDuration**: 配置60秒 → 实际65秒（60 + 5秒缓冲）
- **BattleDuration**: 配置60秒 → 实际65秒（60 + 5秒缓冲，替换原来的2秒网络延时）
- **BuffDuration**: 配置20秒 → 实际25秒（20 + 5秒缓冲）

这确保客户端收到`LCRoundStartNotify`后有足够时间播放动画，服务器不会过早触发超时操作。

### **超时托管机制**

#### **双重超时机制**

系统现在实现了两层超时处理：

1. **Buff选择独立超时**：通过`BuffSelectionTimeoutEvent`触发
2. **阶段整体超时**：通过`BattleTimeoutEvent`触发

#### **Buff选择超时处理**

```csharp
private void OnBuffSelectionTimeout(BuffSelectionTimeoutEvent evt)
{
    // 在BuffDuration+5秒缓冲后触发
    // 强制所有未选择buff的玩家随机选择
    ForceBuffSelectionForAllPlayers();
}
```

#### **阶段整体超时处理**

```csharp
private void OnBattleTimeout(BattleTimeoutEvent evt)
{
    switch (evt.State)
    {
        case BattleState.StatePreparation:
            // 准备阶段超时（65秒），强制所有玩家准备完毕
            ForceAllPlayersReady(); // 只处理准备状态，buff选择已由独立事件处理
            CheckAllPlayersReady();
            break;

        case BattleState.StateBattleInProgress:
            // 战斗超时（65秒），强制结束战斗
            ForceEndAllBattleInstances();
            _battleStateManager.SetState(BattleState.StateRoundSettlement);
            break;

        case BattleState.StateEliminationCheck:
            // 结算确认超时，强制所有玩家确认
            ForceAllPlayersConfirmRound();
            break;
    }
}
```

#### **超时强制处理**

```csharp
private void ForceAllPlayersReady()
{
    var playerIds = _playerManager.GetAllPlayerIds();

    // 如果当前回合有buff选择，先强制未选择buff的玩家随机选择
    if (_battleStateManager.HasBuffSelectionThisRound)
    {
        foreach (var playerId in playerIds)
        {
            var buffOptions = _buffManager.GetPlayerBuffOptions(playerId);
            if (buffOptions.Count > 0)
            {
                bool success = _buffManager.PlayerRandomSelectBuff(playerId);
                if (success)
                {
                    var newHeroes = GenerateHeroesForPlayer(playerId);
                }
            }
        }
    }

    // 强制所有玩家准备完毕
    foreach (var playerId in playerIds)
    {
        if (!_playerManager.IsPlayerReady(playerId))
        {
            _playerManager.SetPlayerReady(playerId, true);
        }
    }
}
```

---

## 6. 机器人托管系统

### **机器人识别**

- **真实玩家**：UID不以9开头
- **机器人**：UID以9开头

### **自动操作时机**

1. **StatePreparation进入时**：
   - 机器人立即选择Buff（如果是Buff回合）并生成英雄
   - 机器人自动准备（`AutoReadyBots()`）
2. **StateBattleInProgress进入时**：
   - 机器人vs机器人：立即随机结算
   - 机器人vs真人：等待真人操作，依赖系统超时机制
3. **StateEliminationCheck进入时**：机器人立即确认结算
4. **真人操作触发时**：真人确认时，所有机器人自动跟随确认
5. **Buff选择超时时**：机器人和真人都会被强制随机选择buff并生成英雄

### **机器人英雄生成时序**

```csharp
private void AutoSelectBuffForBots()
{
    foreach (var playerId in botPlayerIds)
    {
        var buffOptions = _buffManager.GetPlayerBuffOptions(playerId);
        if (buffOptions.Count > 0)
        {
            bool success = _buffManager.PlayerSelectBuff(playerId, selectedBuff);
            if (success)
            {
                // 机器人选择buff后立即生成英雄
                var newHeroes = GenerateHeroesForPlayer(playerId);
                Log.Info($"Generated {newHeroes.Count} new heroes for bot player {playerId}");
            }
        }
    }
}
```

这确保机器人在buff选择回合能正确显示新生成的英雄。

### **战斗结算规则**

#### **1. 真人 vs 机器人**：

```csharp
// 在HandleBattleEnd中自动处理
if (!playerId.ToString().StartsWith("9") && opponentId.ToString().StartsWith("9"))
{
    // 真实玩家发送了结果，机器人自动发送相反结果
    if (!_instanceEndBattleRequests[instanceId].Contains(opponentId))
    {
        _instanceEndBattleRequests[instanceId].Add(opponentId);
        Log.Info($"Auto EndBattle for bot {opponentId} vs real player {playerId}, bot result: {!win}");
    }
}
```

- **结果**：根据真实玩家的战斗结果决定
- **目的**：确保战斗结果的真实性和公平性

#### **2. 机器人 vs 机器人**：

```csharp
// 在AutoHandleBotBattles中立即处理
if (opponentId.ToString().StartsWith("9"))
{
    bool bot1Wins = new Random().Next(2) == 0;
    HandleBattleEnd((ulong)playerId, bot1Wins);
    HandleBattleEnd((ulong)opponentId, !bot1Wins);
}
```

- **结果**：**随机胜负**（50%概率）
- **目的**：保持游戏平衡性，立即结算避免等待

#### **3. 超时强制结算**：

// 超时强制结算：根据减员数量或随机判定

if (player1Id == realPlayerId || player2Id == realPlayerId)

{

  // 有真实玩家参与的战斗超时，随机判定

  winnerId = playerIds[new Random().Next(2)];

}

else

{

  // 两个机器人对战，随机选择获胜者

  winnerId = playerIds[new Random().Next(2)];

}

---

## 7. 对手匹配机制

### 7.1 匹配规则（按策划文档）

#### 7.1.1 第一回合

```csharp
// 随机排序，随机配对
var shuffledPlayers = activePlayers.OrderBy(x => Random.Shared.Next()).ToList();
// 配对：shuffledPlayers[0] vs shuffledPlayers[1], shuffledPlayers[2] vs shuffledPlayers[3]
```

#### 7.1.2 后续回合

```csharp
// 按血量排序：No.1(最高血量) vs No.2, No.3 vs No.4
var rankedPlayers = activePlayers
    .OrderByDescending(p => playerHealths[p]) // 血量高的排前面
    .ThenBy(p => p) // 血量相同时按ID排序
    .ToList();
```

#### 7.1.3 避重机制

```csharp
// 检查是否与上回合对手重复
if (ShouldAvoidPairing(player1, player2))
{
    // 尝试调整配对（与第3个玩家配对）
    if (i + 2 < rankedPlayers.Count)
    {
        var player3 = rankedPlayers[i + 2];
        pairs[player1] = player3; // 调整配对
    }
}
```

#### **4. 淘汰处理**：

- **淘汰1人**：保持4人匹配队列，被淘汰者填充匹配位置
- **淘汰2人**：移出匹配队列，剩余2人对战
- **淘汰3人**：游戏结束

### **配对示例**

**第1回合**：随机配对
- 随机排序：[玩家A, 玩家B, 玩家C, 玩家D]
- 配对结果：A vs B, C vs D

**第2回合**：按血量排序配对
- 血量排序：[玩家C(3血), 玩家A(2血), 玩家D(2血), 玩家B(1血)]
- 配对结果：C vs A, D vs B
- 避重检查：如果C上回合对战过A，则调整为C vs D, A vs B

---

## 8. 资源管理与清理

### **资源清理层次**

#### **1. 回合切换时**：

#### **HandleRoundStart()回合开始处理流程**

```csharp
private void HandleRoundStart()
{
    // 0. 清理上一回合的跟踪数据
    _instanceEndBattleRequests.Clear();
    _roundConfirmedPlayers.Clear();

    // 1. 保存所有玩家的棋盘数据（在创建新实例前）
    var activePlayerIds = _playerManager.GetActivePlayerIds();
    foreach (var playerId in activePlayerIds)
    {
        var instance = _instanceManager.GetInstanceByPlayerId(playerId);
        if (instance != null)
        {
            var entities = instance.CheckerBoard.GetPlayerEntities(playerId);

            // 保存当前棋盘数据用于恢复到新实例
            _playerManager.SavePlayerBoardData(playerId, entities);

            // 保存上一回合结束时的棋盘数据用于对手查看
            _playerManager.SavePlayerPreviousRoundBoardData(playerId, entities);
        }
    }

    // 2. 对手配对
    var opponentPairs = _opponentPairManager.PairOpponents(activePlayerIds, _playerManager);
    _playerManager.SetPlayerOpponents(opponentPairs);

    // 3. 创建战斗实例
    _instanceManager.CreateInstances(opponentPairs);

    // 4. 恢复所有玩家的棋盘数据（在创建新实例后）
    foreach (var playerId in activePlayerIds)
    {
        RestorePlayerBoardDataToInstance(playerId);
    }

    // 5. 清理所有实例中的孤立实体
    foreach (var playerId in activePlayerIds)
    {
        var instance = _instanceManager.GetInstanceByPlayerId(playerId);
        instance?.CheckerBoard.CleanupOrphanedEntities();
    }

    // 6. 清空所有玩家的临时位
    foreach (var playerId in activePlayerIds)
    {
        var instance = _instanceManager.GetInstanceByPlayerId(playerId);
        instance?.CheckerBoard.ClearPlayerTemporarySlots(playerId);
    }

    // 7. 重置玩家准备状态
    _playerManager.ResetPlayersReadyStatus();
}
```

**关键改进**：
- **数据保存时机**：在创建新实例前保存所有玩家的棋盘数据
- **双重保存机制**：同时保存当前数据和历史数据
- **数据恢复**：在新实例中恢复玩家的棋盘数据
- **孤立实体清理**：清理无效的实体引用，防止重复格子问题
- **对手变动兼容**：数据跟随玩家而不是战斗实例

#### **2. 战斗实例销毁**：

// BattleInstance.Dispose()

CheckerBoard?.Dispose();

// BattleInstanceManager.OnClear()

foreach (var instance in _instances.Values)

{

  instance.Dispose();

}

#### **3. 场景销毁**：

// AutoChessScene.Dispose()

_playerManager?.Dispose();

_checkerBoard?.Dispose();

_battleStateManager?.Dispose();

_buffManager?.Dispose();

#### **4. 服务层清理**：

// BattleService.CleanupBattle()

_battles.TryRemove(battleId, out _);

_sceneManager.TryRemove(battleId, out var scene);

SceneManager.Instance.RemoveAutoChessScene(battleId);

#### **5. 玩家登出清理**：

// BattleService.CleanupBattleByPlayerId()

if (_playerToBattle.TryGetValue(playerId, out long battleId))

{

  var scene = _sceneManager.GetValueOrDefault(battleId);

  var realPlayerCount = scene.GetAllPlayerIds().Count(id => !id.ToString().StartsWith("9"));



  if (realPlayerCount <= 1)

  {

    // 只有一个真实玩家，清理整个战斗

    CleanupBattle(battleId);

  }

  else

  {

    // 多个真实玩家，只移除当前玩家映射

    _playerToBattle.TryRemove(playerId, out _);

  }

}

### **清理时机**

- **每回合开始**：清理上一回合跟踪数据
- **对手重新配对**：销毁旧战斗实例，创建新实例
- **游戏结束**：完整清理所有资源
- **玩家登出**：智能清理玩家相关资源
- **异常情况**：定时清理超时战斗

---

## 9. 玩家登出清理机制

### **架构设计**

玩家登出清理机制采用**双端协同**的设计，确保战斗资源的完整清理：

```
GameServer (玩家登出触发)
    ↓ LeaveBattle RPC
BattleServer (资源清理执行)
    ↓ 智能清理策略
战斗资源完整清理
```

### **清理流程**

#### **1. GameServer端触发**

```go
// player_handler.go - 玩家登出处理
func (p *Player) playerLogout() {
    // ... 现有登出逻辑 ...

    // 清理战斗相关资源
    p.battle.cleanupBattleOnLogout()

    // ... 其他清理逻辑 ...
}

// player_battle.go - 战斗资源清理
func (b *Battle) cleanupBattleOnLogout() {
    if b.battleServerId == "" && b.battleId == 0 {
        return // 玩家不在战斗中
    }

    // 调用BattleServer的LeaveBattle RPC接口
    leaveBattleReq := &natsrpc.LeaveBattleReq{Uid: b.player.Uid()}
    err := battleServiceClient.LeaveBattle(ctx, leaveBattleReq, b.battleServerId)

    // 清理本地状态
    b.battleServerId = ""
    b.battleId = 0
}
```

#### **2. BattleServer端执行**

```csharp
// BattleService.cs - RPC接口实现
public LeaveBattleResp LeaveBattle(LeaveBattleReq request)
{
    var uid = (long)request.Uid;

    // 参数验证
    if (uid <= 0) return new LeaveBattleResp { Code = -2 };

    // 检查玩家是否在战斗中
    if (!_playerToBattle.ContainsKey(uid))
        return new LeaveBattleResp { Code = 0 }; // 已经不在战斗中

    // 执行清理逻辑
    CleanupBattleByPlayerId(uid);

    return new LeaveBattleResp { Code = 0 };
}
```

### **智能清理策略**

#### **策略1：单玩家战斗清理**
```csharp
if (realPlayerCount <= 1)
{
    Log.Info($"Only one real player in battle {battleId}, cleaning up entire battle");
    CleanupBattle(battleId); // 清理整个战斗
}
```

**适用场景**：
- 测试环境：1真人 + 3机器人
- 其他玩家已离线：只剩最后一个真实玩家

**清理内容**：
- 移除战斗状态映射
- 销毁AutoChessScene实例
- 清理所有相关资源

#### **策略2：多玩家战斗保持**
```csharp
else
{
    _playerToBattle.TryRemove(playerId, out _);
    Log.Info($"Removed player {playerId}, battle continues with other players");
}
```

**适用场景**：
- 生产环境：多个真实玩家参与
- 部分玩家离线：战斗继续进行

**清理内容**：
- 仅移除离线玩家的映射关系
- 保持战斗继续进行
- 其他玩家不受影响

### **协议定义**

#### **LeaveBattle RPC接口**
```protobuf
// 离开战斗请求
message LeaveBattleReq {
    uint64 uid = 1;  // 玩家ID
}

// 离开战斗响应
message LeaveBattleResp {
    int32 code = 1;  // 响应码：0=成功，-1=失败，-2=参数错误
}

// RPC服务定义
service BattleService {
    rpc LeaveBattle(LeaveBattleReq) returns (LeaveBattleResp) {
        option (use_server_id) = true;
    }
}
```

### **通信模式**

#### **NATS Publish模式**
```go
// GameServer端 - 单向发送
func (c *battleServiceClient) LeaveBattle(ctx context.Context, req *LeaveBattleReq, serverId string) error {
    return c.client.Publish("/"+serverId+"/natsrpc.BattleService/LeaveBattle", req)
}
```

**特点**：
- **异步处理**：不阻塞玩家登出流程
- **容错性强**：网络异常不影响登出
- **性能优化**：减少登出延迟

### **状态管理**

#### **GameServer端状态**
```go
type Battle struct {
    player         *Player
    battleServerId string  // BattleServer ID
    battleId       int64   // 战斗ID
}

// 状态检查方法
func (b *Battle) IsInBattle() bool {
    return b.battleServerId != "" && b.battleId != 0
}

// 强制清理状态（修复状态不一致）
func (b *Battle) ForceClearBattleState() {
    if b.battleServerId != "" || b.battleId != 0 {
        log.Warning("force clearing inconsistent battle state")
        b.battleServerId = ""
        b.battleId = 0
    }
}
```

#### **状态一致性处理**

**问题**：玩家登出重登录后，可能出现`battleServerId`有值但`battleId`为0的状态不一致情况。

**解决方案**：
1. **匹配成功时同步设置**：同时设置`battleServerId`和`battleId`
2. **状态检查增强**：在`RoundBattleStart`中检测状态不一致并自动修复
3. **强制清理机制**：提供`ForceClearBattleState()`处理异常状态

```go
// 修复后的匹配成功处理
func (p *Player) matchResultHandler(ctx context.Context, msg *actor.Message) error {
    data := msg.Data.(*natsrpc.MatchResultReq)

    // 同时设置battleServerId和battleId
    p.battle.battleServerId = strconv.FormatInt(data.ServerId, 10)
    p.battle.battleId = data.BattleId

    return nil
}

// 状态不一致检测和修复
func (b *Battle) RoundBattleStart(ctx context.Context, req *cs.CLRoundBattleStartReq) *cs.LCRoundBattleStartResp {
    if !b.IsInBattle() {
        // 检测状态不一致并自动修复
        if b.battleServerId != "" && b.battleId == 0 {
            log.Warning("detected inconsistent battle state, force clearing")
            b.ForceClearBattleState()
        }
        return &cs.LCRoundBattleStartResp{Code: int32(error_code.ERROR_BATTLE_NOT_EXIST)}
    }
    // ... 正常处理逻辑
}
```

#### **BattleServer端状态**
```csharp
// 玩家到战斗的映射
private static readonly ConcurrentDictionary<long, long> _playerToBattle;

// 战斗状态管理
private static readonly ConcurrentDictionary<long, BattleState> _battles;

// 场景实例管理
private static readonly ConcurrentDictionary<long, AutoChessScene> _sceneManager;
```

### **错误处理**

#### **网络异常处理**
```go
err := battleServiceClient.LeaveBattle(ctx, leaveBattleReq, b.battleServerId)
if err != nil {
    log.Error("LeaveBattle RPC failed", log.Err(err))
    // 即使RPC失败，也继续清理本地状态
}
// 清理本地状态
b.battleServerId = ""
b.battleId = 0
```

#### **参数验证**
```csharp
if (uid <= 0)
{
    Log.Warning($"Invalid player ID in LeaveBattle request: {uid}");
    return new LeaveBattleResp { Code = -2 };
}
```

### **监控与日志**

#### **关键日志点**
```csharp
// 请求接收
Log.Info($"LeaveBattle request received from player {uid}");

// 清理策略选择
Log.Info($"Only one real player in battle {battleId}, cleaning up entire battle");
Log.Info($"Removed player {playerId}, battle continues with other players");

// 清理完成
Log.Info($"LeaveBattle completed successfully for player {uid}");
```

#### **错误日志**
```csharp
Log.Error($"LeaveBattle error for player {request?.Uid}: {ex.Message}");
Log.Error($"LeaveBattle stack trace: {ex.StackTrace}");
```

### **测试场景**

#### **场景1：单玩家测试环境**
- **输入**：1真人 + 3机器人，真人登出
- **预期**：整个战斗被清理，所有资源释放
- **验证**：BattleService状态为空，场景实例被销毁

#### **场景2：多玩家生产环境**
- **输入**：4真人，1人登出
- **预期**：只移除登出玩家，其他3人继续战斗
- **验证**：战斗继续，只有玩家映射被移除

#### **场景3：网络异常**
- **输入**：RPC调用失败
- **预期**：GameServer本地状态仍被清理
- **验证**：玩家状态重置，不影响后续操作

### **性能优化**

#### **异步处理**
- 使用NATS Publish模式，避免阻塞
- 本地状态立即清理，不等待远程响应

#### **内存管理**
- 及时清理ConcurrentDictionary中的映射
- 智能判断是否需要清理整个战斗实例

#### **并发安全**
- 全面使用线程安全的数据结构
- 原子操作保证状态一致性

---

## 10. 战斗流程控制

### **状态转换等待机制**

#### **核心修复**：

- ✅ **StatePreparation → StateBattleStarting**：等待所有玩家`ReadyBattleReq`
- ✅ **StateBattleInProgress → StateRoundSettlement**：等待所有玩家`EndBattleReq`
- ✅ **StateEliminationCheck → StateRoundStart**：等待所有玩家`EnterBattleReq`（回合确认）

#### **EnterBattle逻辑修复**：

// 区分初始进入和回合确认

if (currentBattleState == BattleState.StateNone)

{

  // 第一次进入：正常处理玩家进入

  enteredPlayers.Add(playerId);

  if (enteredPlayers.Count >= allPlayers.Count)

  {

​    scene.StartBattleStateMachine();

  }

}

else

{

  // 回合确认：通知Scene处理新回合确认

  scene.HandleRoundConfirmation(playerId);

}

### **RoundBattleEnd优化**

#### **按实例推送机制**：

// 当同一实例的2名玩家都发送EndBattleReq后，立即推送给他们

var instanceRequests = _instanceEndBattleRequests[instanceId];

if (instanceRequests.Count >= 2)

{

  // 确定胜负并推送结果

  var winnerId = win ? playerId : opponentId;

  var loserId = win ? opponentId : playerId;

  

  // 立即推送RoundBattleEnd给这两名玩家

  NotifyGameServerRoundBattleEnd(winnerId, loserId);

  

  // 清理该实例的跟踪数据

  _instanceEndBattleRequests.Remove(instanceId);

}

### **血量和胜负机制**

#### **血量计算系统**：

**配置来源**：PlayMode表的PlayerHP字段
```json
"PlayerHP": [[0,999,3],[1000,100000,4]]
```

**计算规则**：
- **统一血量原则**：所有玩家使用相同血量值
- **最高杯段原则**：取4名玩家中最高奖杯数对应的血量
- **超限处理**：超过配置上限时使用最高血量值

**实现位置**：
- **GameServer**：在`matchResultHandler`中计算血量并设置到`LCMatchSuccessNotify`
- **BattleServer**：在`PlayerManager.Initialize`中使用相同逻辑计算血量

**血量配置示例**：
- **0-999奖杯**：3血
- **1000+奖杯**：4血

#### **胜负判定**：

1. **一方全灭**：对方获胜
2. **双方全灭**：平局，双方扣1血
3. **战斗超时**：按减员数量判定，减员多的败，相同则平局

#### **游戏结束条件**：

- **3人生命值为0**：剩余1人获胜
- **血量为0不再扣除**：避免负血量显示

---

## 11. 性能指标

### 11.1 单回合时间

- **理想情况**（真人操作）：约125秒/回合
- **机器人优化**（1真人+3机器人）：约65秒/回合
- **超时兜底**：最多127秒/回合

### 11.2 并发支持

- **线程安全**：全面使用ConcurrentDictionary
- **事件驱动**：异步处理，高并发支持
- **资源管理**：完善的清理机制，避免内存泄漏

### 11.3 可靠性保证

- **超时托管**：所有关键节点都有超时处理
- **错误恢复**：完善的异常处理和日志记录
- **状态一致性**：原子操作保证数据一致性

---

## 12. 事件驱动架构

### **BattleEventBus设计**

AutoChessScene使用事件总线模式实现组件间的解耦通信：

```csharp
// 事件注册
_eventBus.Subscribe<BattleStateChangedEvent>(OnBattleStateChanged);
_eventBus.Subscribe<AllPlayersReadyEvent>(OnAllPlayersReady);
_eventBus.Subscribe<PlayerEliminatedEvent>(OnPlayerEliminated);

// 事件发布
_eventBus.Publish(new BattleStateChangedEvent(oldState, newState, countdown));
```

### **核心事件类型**

#### **状态事件**
- `BattleStateChangedEvent`：战斗状态变化
- `RoundStartedEvent`：回合开始
- `BattleTimeoutEvent`：战斗超时

#### **玩家事件**
- `AllPlayersReadyEvent`：所有玩家准备完毕
- `PlayerEliminatedEvent`：玩家被淘汰
- `GameOverEvent`：游戏结束

#### **实体事件**
- `EntityCreatedEvent`：实体创建
- `EntityMovedEvent`：实体移动
- `EntityMergedEvent`：实体合成
- `EntityRemovedEvent`：实体移除

### **事件处理流程**

1. **状态变化触发**：BattleStateManager发布状态变化事件
2. **组件响应**：各组件订阅相关事件并执行对应逻辑
3. **级联处理**：一个事件可能触发多个组件的响应
4. **异步执行**：事件处理不阻塞主流程


---

## 13. 总结

AutoChess BattleServer 是一个复杂的多人实时战斗系统，通过精心设计的组件化架构和状态管理，实现了稳定可靠的4人自走棋对战功能。

### 13.1 核心特性

- **多人实时对战**：支持4人同时进行的自走棋战斗
- **跨服务器支持**：玩家可以来自不同的GameServer
- **状态同步机制**：实时同步战斗状态到GameServer
- **Buff选择系统**：回合制的策略选择机制
- **英雄合成系统**：复杂的棋子合成和升级逻辑
- **坐标转换系统**：客户端相对坐标与服务器绝对坐标的转换
- **AI对战支持**：奇数玩家与AI对战的完整实现

### 13.2 技术亮点

- **组件化设计**：清晰的职责分离和依赖注入
- **事件驱动架构**：基于EventBus的松耦合通信
- **状态机管理**：严格的战斗状态流转控制
- **内存池优化**：高效的对象复用机制
- **NATS RPC通信**：可靠的跨服务通信
- **坐标系统设计**：复杂的多视角坐标转换机制

### 13.3 系统稳定性

系统已经能够稳定支持复杂的多人自走棋对战场景，包括GridID唯一性保证、SelectBuff功能完整性、奇数玩家AI对战支持、回合间状态一致性等，现已达到生产环境可用的稳定性水平。
