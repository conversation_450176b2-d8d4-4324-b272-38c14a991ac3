//GrpcAddressType:Rank
//GrpcServerType:server,world

syntax = "proto3";

package Aurora.PlayerInfoServer;

import "google/api/annotations.proto";
import "playerinfo/v1/playerinfostruct.proto";

option go_package = "gameserver/api/rankservice/v1;v1";

//ServiceStart
service RankService {
rpc SyncRoleInfo(SyncRoleInfoRequest) returns (SyncRoleInfoReply) {
}

rpc UpdateRoleRankInfo(UpdateRoleRankRequest) returns (UpdateRoleRankReply) {
}

rpc RemoveRole(RemoveRoleRequest) returns (RemoveRoleReply) {
}

rpc SyncGuildInfo(SyncGuildInfoRequest) returns (SyncGuildInfoReply) {
}

rpc UpdateGuildRankInfo(UpdateGuildRankRequest) returns (UpdateGuildRankReply) {
}

rpc RemoveGuild(RemoveGuildRequest) returns (RemoveGuildReply) {
}

rpc SyncPetInfo(SyncPetInfoRequest) returns (SyncPetInfoReply) {
}

rpc UpdatePetRankInfo(UpdatePetRankRequest) returns (UpdatePetRankReply) {
}

rpc RemovePet(RemovePetRequest) returns (RemovePetReply) {
}

rpc RemoveTwinsTreeData(RemoveTwinsDataRequest) returns (RemoveTwinsDataReply) {
}

rpc AddGuildMember(GuildAddMemberRequest) returns (GuildMemberReply) {
}

rpc RemoveGuildMember(GuildRemoveMemberRequest) returns (GuildMemberReply) {
}

rpc GetGuildMember(GuildGetAllMemberRequest) returns (GuildGetAllMemberReply) {
}
}
//ServiceEnd

service RankListHttp {
rpc GetRankListData (GetRankListReq) returns (GetRankListReply) {
option (google.api.http) = {
post : "/api/rankservice/ranklist",
body : "*",
};
}

rpc GetTwinTreeRankList (GetTwinsTreeRankListReq) returns (GetTwinsTreeRankListReply) {
option (google.api.http) = {
post : "/api/rankservice/twinstree",
body : "*",
};
}

rpc GetPetInfo(GetPetInfoReq) returns (GetPetInfoReply) {
option (google.api.http) = {
post : "/api/rankservice/petinfo",
body : "*",
};
}
}

enum RankGroupType
{
RoleRank = 0;
GuildRank = 1;
TeamRank = 2;
PetRank = 3;
}

//Type:Http
enum RankType
{
RankTypeNone = 0;
//总评分
FightPoint = 101;
//装备
Equip = 102;
//等级
Level = 103;
//职级or驭灵师等级
MasterPath = 104;
//幻灵总评分
Pet = 201;
//繁荣度(暂无)
Guild = 301;
// 公会等级排行
GuildLevel = 302;
// 公会捐献度排行
GuildContribution = 303;
//战分（双生树积分）
TTChasingScore = 401;
//混沌之塔
HeroTrail = 601;
//梦魇之城
SecretTerritory = 605;
}

message RankRoleInfo
{
uint64  guid = 1;
int32   zwid = 2;
int32   kserver = 3;
string  name = 4;
int32   sex = 5;
int32   level = 6;
int64   fightPoint = 7;
uint64  guildId = 8;
}

message SyncRoleInfoRequest
{
RankRoleInfo info = 1;
}

// The response message containing the message
message SyncRoleInfoReply
{
int32 result = 1;
}

message RankValueInfo
{
int32  rankType = 1;
int64  rankValue = 2;
}

message UpdateRoleRankRequest
{
uint64   guid = 1;
int32   zwid = 2;
int32   kserver = 3;
repeated RankValueInfo info = 4;
}

message UpdateRoleRankReply
{
int32 result = 1;
}

message RankGuildInfo
{
uint64    guid = 1;        // 公会ID
int32     zwid = 2;        // 区服ID
string    guildName  = 3;  // 公会名称
int32     level = 4;       // 公会等级
uint64    masterGuid = 5;  // 会长ID
int32     memberCount = 6; // 公会成员数量
string    guildIcon = 7;   // 公会图标
string	  masterName = 8;  // 会长名字
string    notice = 9;      // 公会宣言
}

message SyncGuildInfoRequest
{
RankGuildInfo info = 1;
}

message SyncGuildInfoReply
{
int32 result = 1;
}

message UpdateGuildRankRequest
{
uint64   guid = 1;
int32   zwid = 2;
int32   kserver = 3;
repeated RankValueInfo info = 4;
}

message UpdateGuildRankReply
{
int32 result = 1;
}

//Type:Inner
message RankPetInfo
{
uint64 GUID = 1;
uint64 OwnerId = 2;
PetInfoAttr_PISct PetInfo = 10;
}

//Type:Inner
message PetRankValueInfo
{
int32 rankType = 1;
int64 rankValue = 2;
uint64 guid = 3;
}

//Type:Inner
message RemoveInfo
{
uint64     guid = 1;
int32     zwid = 2;
int32     kserver = 3;
}

//Type:Inner
message PetRemoveInfo
{
int32 PetID = 1;
int32 rankType = 2;
RemoveInfo pet = 3;
}

//Type:Inner
//Target:S2W
message SyncPetRankInfo //IMessage
{
repeated PetRankValueInfo rankInfo = 1;
repeated RankPetInfo petInfo = 2;
repeated PetRemoveInfo petRem = 3;
}

message SyncPetInfoRequest
{
repeated RankPetInfo info = 1;
}

message SyncPetInfoReply
{
int32 result = 1;
}

message UpdatePetRankRequest
{
int32   zwid = 2;
int32   kserver = 3;
repeated PetRankValueInfo info = 4;
}

message UpdatePetRankReply
{
int32 result = 1;
}


message RemoveRoleRequest
{
RemoveInfo role = 1;
}

message  RemoveRoleReply
{
int32 result = 1;
}

message RemoveGuildRequest
{
RemoveInfo guild = 1;
}

message  RemoveGuildReply
{
int32 result = 1;
}

message RemovePetRequest
{
int32 PetID = 1;
int32 RankType = 2;
RemoveInfo pet = 3;
}

message  RemovePetReply
{
int32 result = 1;
}


message RemoveTwinsDataRequest
{
int32 zwid = 1;
}

message RemoveTwinsDataReply
{
int32 result = 1;
int32 zwid = 2;
}

//Type:Http
message RoleRankInfo
{
uint64  guid = 1;
int32   zwid = 2;
string  name = 3;
int32   sex   = 4;
int32   level = 5;
int64   fightPoint = 6;
int32   rank = 7;
int32   tilte = 8;
string  guildName = 9;
}

//Type:Http
message GuildRankInfo
{
  uint64    guid = 1;        // 公会ID
  int32     zwid = 2;        // 区服ID
  string    guildName = 3;   // 公会名称
  int32     level = 4;       // 公会等级
  uint64    masterGuid = 6;  // 会长ID
  int32     rank = 7;        // 排名
  int32     memberCount = 8; // 公会成员数量
  string    guildIcon = 9;   // 公会图标
  string	  masterName = 10; // 会长名字
  string    notice = 11;     // 公会宣言
}

//Type:Http
message PetRankInfo
{
uint64    guid = 1;
uint64    ownerId = 2;
string    name = 3;
int32     petID = 4;
string    petName = 5;
int32     rank = 6;
}

//Type:Http
message RankDataInfo
{
RoleRankInfo  role = 1;
GuildRankInfo guild = 2;
PetRankInfo   pet = 3;
int64  rankValue = 10;
int64  rankValue2 = 11;
}

//Type:Http
message GetRankListReq
{
uint64 charGuid = 1;
//排行榜大类 0：角色 1：公会 2：队伍 3:单个幻灵
int32 rankGroup = 3;
//排行榜类型
int32 rankType = 4;
//三级页签 0：总榜 1：好友
int32 rankFlag = 5;
//分页 0：全部  （总共100行&每页50行）
int32 rankPage = 6;
}

//Type:Http
message GetRankListReply
{
uint64 charGuid = 1;
int32 rankGroup = 2;
int32 rankType = 3;
int32 rankFlag = 4;
int32 rankPage = 5;
repeated RankDataInfo data = 10;
RankDataInfo  selfInfo = 20;
}

//Type:Http
message GetTwinsTreeRankListReq
{
uint64 charGuid = 1;
//分页 0：全部  （总共100行&每页50行）
int32 rankPage = 2;
}

//Type:Http
message TwinsTreeRankInfo
{
uint64  charGuid = 1;
int32   zwid = 2;
string  name = 3;
int32   sex  = 4;
string  icon = 5;
int32   rank = 10;
int64   rankValue = 11;
}

//Type:Http
message GetTwinsTreeRankListReply
{
uint64 charGuid = 1;
int32 rankPage = 2;
repeated TwinsTreeRankInfo data = 10;
TwinsTreeRankInfo  selfInfo = 20;
}

//Type:Http
message GetPetInfoReq
{
uint64 GUID = 1;
}

//Type:Http
message GetPetInfoReply
{
uint64 GUID = 1;
int32  Ret = 2;
uint64 OwnerId = 3;
PetInfoAttr_PISct PetInfo = 10;
}

message GuildAddMemberRequest
{
uint64 charGuid = 1;
uint64 guildGuid = 2;
}

message GuildRemoveMemberRequest
{
uint64 charGuid = 1;
uint64 guildGuid = 2;
}

message GuildMemberReply
{
int32 result = 1;
}

message GuildGetAllMemberRequest
{
uint64 guildGuid = 1;
}

message GuildGetAllMemberReply
{
int32 result = 1;
uint64 guildGuid = 2;
repeated uint64 data = 3;
}


